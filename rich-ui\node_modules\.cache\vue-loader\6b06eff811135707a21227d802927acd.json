{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\components\\VatinvoiceDialog.vue?vue&type=style&index=0&id=bbf53860&scoped=true&lang=css&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\components\\VatinvoiceDialog.vue", "mtime": 1756454512254}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnllbGxvdy1iZyB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmZiZTY7DQp9DQoNCi5kaXNhYmxlLWZvcm0gew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KICBib3JkZXItY29sb3I6ICNlNGU3ZWQ7DQogIGNvbG9yOiAjYzBjNGNjOw0KICBjdXJzb3I6IG5vdC1hbGxvd2VkOw0KfQ0KDQoudXBsb2FkZWQtZmlsZS1wcmV2aWV3IHsNCiAgbWFyZ2luLXRvcDogMTBweDsNCiAgcGFkZGluZzogMTBweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOw0KfQ0KDQoucHJldmlldy10aXRsZSB7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBjb2xvcjogIzQ5NTA1NzsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5maWxlLWxpbmtzIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiAxMHB4Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQp9DQoNCi5maWxlLWxpbmtzIC5lbC1saW5rIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KfQ0KDQovKiB0cmVlc2VsZWN057uE5Lu25qC35byPICovDQo6ZGVlcCgudnVlLXRyZWVzZWxlY3RfX21lbnUpIHsNCiAgei1pbmRleDogOTk5OSAhaW1wb3J0YW50Ow0KICBwb3NpdGlvbjogZml4ZWQgIWltcG9ydGFudDsNCn0NCg0KOmRlZXAoLnZ1ZS10cmVlc2VsZWN0X19tZW51LWNvbnRhaW5lcikgew0KICB6LWluZGV4OiA5OTk5ICFpbXBvcnRhbnQ7DQp9DQoNCjpkZWVwKC52dWUtdHJlZXNlbGVjdF9fZHJvcGRvd24pIHsNCiAgei1pbmRleDogOTk5OSAhaW1wb3J0YW50Ow0KfQ0KDQovKiDnoa7kv53kuIvmi4nmoYblnKjooajmoLzkuYvkuIogKi8NCjpkZWVwKC52dWUtdHJlZXNlbGVjdF9fbWVudS1hcnJvdykgew0KICB6LWluZGV4OiA5OTk5ICFpbXBvcnRhbnQ7DQp9DQoNCjpkZWVwKC52dWUtdHJlZXNlbGVjdF9fbGlzdCkgew0KICB6LWluZGV4OiA5OTk5ICFpbXBvcnRhbnQ7DQp9DQoNCi8qIOmHkemineWFrOW8j+agt+W8jyAqLw0KLmFtb3VudC1mb3JtdWxhIHsNCiAgbWFyZ2luLXRvcDogNXB4Ow0KICBwYWRkaW5nOiA1cHggOHB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhOw0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzQwOWVmZjsNCn0NCg0KLmFtb3VudC1mb3JtdWxhIHNtYWxsIHsNCiAgY29sb3I6ICM2MDYyNjY7DQogIGZvbnQtc2l6ZTogMTJweDsNCiAgbGluZS1oZWlnaHQ6IDEuNDsNCn0NCg0KLnRleHQtbXV0ZWQgew0KICBjb2xvcjogIzkwOTM5OSAhaW1wb3J0YW50Ow0KfQ0K"}, {"version": 3, "sources": ["VatinvoiceDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAm2CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "VatinvoiceDialog.vue", "sourceRoot": "src/views/system/vatinvoice/components", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    v-dialogDrag\r\n    v-dialogDragWidth\r\n    :close-on-click-modal=\"false\"\r\n    :modal-append-to-body=\"false\"\r\n    :title=\"title\"\r\n    :visible.sync=\"dialogVisible\"\r\n    append-to-body\r\n    modal\r\n    width=\"80%\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <el-form ref=\"form\" :model=\"formData\" :rules=\"rules\" class=\"edit\" label-width=\"80px\" size=\"mini\">\r\n      <!-- 第一行 - 基本信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票流水号\">\r\n            <el-input v-model=\"formData.invoiceCodeNo\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                      placeholder=\"发票流水号\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"进销标志\">\r\n            <el-select v-model=\"formData.saleBuy\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"进销标志\"\r\n                       style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"销项\" value=\"sale\"/>\r\n              <el-option label=\"进项\" value=\"buy\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票性质\">\r\n            <!-- 主营业务收入/非主营收入/营业外收入/成本/费用 -->\r\n            <el-select v-model=\"formData.taxClass\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"发票性质\"\r\n                       style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"主营业务收入\" value=\"主营业务收入\"/>\r\n              <el-option label=\"非主营业务收入\" value=\"非主营业务收入\"/>\r\n              <el-option label=\"营业外收入\" value=\"营业外收入\"/>\r\n              <el-option label=\"成本\" value=\"成本\"/>\r\n              <el-option label=\"费用\" value=\"费用\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票类型\">\r\n            <el-select v-model=\"formData.invoiceType\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"发票类型\" style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"增值税专用发票\" value=\"增值税专用发票\"/>\r\n              <el-option label=\"普通发票\" value=\"普通发票\"/>\r\n              <el-option label=\"收据\" value=\"收据\"/>\r\n              <el-option label=\"无票收支\" value=\"无票收支\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"接收合开\">\r\n            <el-row>\r\n              <el-col :offset=\"1\" :span=\"8\">\r\n                <el-checkbox v-model=\"formData.mergeInvoice\" :class=\"{'disable-form': isDisabled()}\"\r\n                             :disabled=\"isDisabled()\" false-label=\"0\"\r\n                             true-label=\"1\"\r\n                >\r\n                </el-checkbox>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-tag :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                        :type=\"getInvoiceStatusType(formData.invoiceStatus)\"\r\n                >{{ getInvoiceStatusText(formData.invoiceStatus) }}\r\n                </el-tag>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票号码\">\r\n            <el-row>\r\n              <el-col :span=\"18\">\r\n                <el-input v-model=\"formData.invoiceOfficalNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"发票号码\"\r\n                />\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-popover\r\n                  placement=\"top-start\"\r\n                  title=\"发票附件管理\"\r\n                  trigger=\"hover\"\r\n                  width=\"300\"\r\n                >\r\n                  <div>\r\n                    <file-upload\r\n                      :class=\"isDisabled()?'disable-form':''\"\r\n                      :file-type=\"['pdf']\"\r\n                      :is-tip-flex=\"true\"\r\n                      :value=\"formData.invoiceAttachment\"\r\n                      @input=\"formData.invoiceAttachment=$event\"\r\n                    />\r\n                  </div>\r\n                  <template #reference>\r\n                    <el-button size=\"mini\" type=\"text\">\r\n                      [发票]\r\n                    </el-button>\r\n                  </template>\r\n                </el-popover>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二行 - 公司和账户信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"16\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"所属公司\">\r\n                <el-input v-model=\"formData.invoiceBelongsTo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"所属公司\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"我司账户\">\r\n                <tree-select :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"formData.richBankCode\" :placeholder=\"'银行账户'\"\r\n                             :type=\"'companyAccount'\" @return=\"formData.richBankCode=$event\"\r\n                             :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                             @returnData=\"handleRichBankCodeChange\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"对方公司\">\r\n                <company-select :load-options=\"companyList\"\r\n                                :multiple=\"false\" :no-parent=\"true\"\r\n                                :pass=\"formData.cooperatorId\" :placeholder=\"''\"\r\n                                @return=\"formData.cooperatorId=$event\"\r\n                                :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"对方账户\">\r\n                <el-select v-model=\"formData.cooperatorBankCode\" :class=\"{'disable-form': isDisabled()}\"\r\n                           :disabled=\"isDisabled()\"\r\n                           placeholder=\"对方账户\" style=\"width: 100%\"\r\n                           @change=\"handleBankAccountChange\" @click.native=\"fetchCompanyBankAccounts\"\r\n                >\r\n                  <el-option v-for=\"item in availableBankList\" :key=\"item.bankAccId\"\r\n                             :label=\"item.bankAccCode+ '('+item.bankAccount+')'\" :value=\"item.bankAccCode\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发票抬头\">\r\n                <el-input v-model=\"formData.richCompanyTitle\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司发票抬头\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发票抬头\">\r\n                <el-input v-model=\"formData.cooperatorCompanyTitle\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方发票抬头\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n\r\n        <el-col :span=\"8\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"formData.opRemark\" :minrows=\"3\" :rows=\"2\"\r\n                        placeholder=\"开票要求\" type=\"textarea\"\r\n                        :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n              />\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"formData.relatedOrderNo\" :minrows=\"3\" :rows=\"2\" placeholder=\"相关订单号\"\r\n                        type=\"textarea\"\r\n                        :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n              />\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第四行 - 税号信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"16\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"税号\">\r\n                <el-input v-model=\"formData.richVatSerialNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司纳税人识别号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"税号\">\r\n                <el-input v-model=\"formData.cooperatorVatSerialNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\" placeholder=\"对方纳税人识别号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行全称\">\r\n                <el-input v-model=\"formData.richBankFullname\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司银行全称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行全称\">\r\n                <el-input v-model=\"formData.cooperatorBankFullname\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方银行全称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行账号\">\r\n                <el-input v-model=\"formData.richBankAccount\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司账号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行账号\">\r\n                <el-input v-model=\"formData.cooperatorBankAccount\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方账号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"结算币种\">\r\n                <tree-select :class=\"{'disable-form': isDisabled()}\"\r\n                             :disabled=\"isDisabled()\" :pass=\"formData.settlementCurrencyCode\"\r\n                             :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                             style=\"width: 100%\" @return=\"formData.settlementCurrencyCode=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"结算金额\">\r\n                <el-input v-model=\"formData.settlementAmount\" class=\"disable-form\"\r\n                          disabled placeholder=\"结算金额\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"发票汇率\">\r\n                <el-input v-model=\"formData.invoiceExchangeRate\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"发票汇率\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"发票金额\">\r\n                <el-input v-model=\"formData.invoiceAmount\" :placeholder=\"getInvoiceAmountPlaceholder()\"\r\n                          class=\"disable-form\"\r\n                          disabled\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n\r\n        <el-col :span=\"4\">\r\n          <el-input v-model=\"formData.invoiceRemark\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                    :minrows=\"6\" :rows=\"6\"\r\n                    placeholder=\"税务发票备注栏\" type=\"textarea\"\r\n          />\r\n        </el-col>\r\n\r\n        <el-col :span=\"4\">\r\n          <el-col>\r\n            <el-form-item label=\"期望支付日\">\r\n              <el-date-picker v-model=\"formData.expectedPayDate\"\r\n                              clearable\r\n                              placeholder=\"期望支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"批复支付日\">\r\n              <el-date-picker v-model=\"formData.approvedPayDate\"\r\n                              clearable\r\n                              placeholder=\"批复支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"实际支付日\">\r\n              <el-date-picker v-model=\"formData.actualPayDate\"\r\n                              clearable\r\n                              placeholder=\"实际支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"报税月份\">\r\n              <el-input v-model=\"formData.belongsToMonth\"\r\n                        :class=\"{'disable-form': isDisabled() || type === 'debitNote'}\"\r\n                        :disabled=\"isDisabled() || type === 'debitNote'\"\r\n                        class=\"yellow-bg\" placeholder=\"2025/7/31\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-col>\r\n      </el-row>\r\n\r\n\r\n      <el-divider></el-divider>\r\n\r\n      <!-- 发票明细表格 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"24\">\r\n          <!-- 批量操作工具栏 -->\r\n          <div style=\"margin-bottom: 10px; display: flex; align-items: center; gap: 10px;\">\r\n            <el-button\r\n              :disabled=\"isDisabled() || !selectedRows.length\"\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"showBatchInvoicingItemDialog\"\r\n            >\r\n              批量设置开票项目\r\n            </el-button>\r\n            <el-button\r\n              :disabled=\"isDisabled() || !selectedRows.length\"\r\n              icon=\"el-icon-check\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"batchSetDefaultInvoicingItem\"\r\n            >\r\n              批量设置默认项目\r\n            </el-button>\r\n            <el-button\r\n              :disabled=\"isDisabled() || !selectedRows.length\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"batchClearInvoicingItem\"\r\n            >\r\n              批量清空开票项目\r\n            </el-button>\r\n            <span style=\"margin-left: 20px; color: #606266;\">\r\n              已选择 {{ selectedRows.length }} 项\r\n            </span>\r\n          </div>\r\n\r\n          <el-table\r\n            ref=\"chargeTable\"\r\n            :data=\"formData.rsChargeList\"\r\n            border\r\n            size=\"mini\"\r\n            style=\"width: 100%\"\r\n            @selection-change=\"handleSelectionChange\"\r\n          >\r\n            <el-table-column align=\"center\" type=\"selection\" width=\"35\"/>\r\n            <el-table-column align=\"center\" label=\"账单编号\" prop=\"sqdInvoiceCodeNo\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"RCT号\" prop=\"sqdRctNo\"/>\r\n            <el-table-column align=\"center\" label=\"所属服务\" prop=\"serviceLocalName\" width=\"100\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.sqdServiceTypeId == 0 ? \"客户应收\" : scope.row.serviceLocalName }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"费用名称\" prop=\"chargeName\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"备注\" prop=\"chargeRemark\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"收付标志\" prop=\"isReceivingOrPaying\" width=\"80\">\r\n              <template #default=\"scope\">{{ scope.row.isReceivingOrPaying == 0 ? \"应收\" : \"应付\" }}</template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"报价币种\" prop=\"quoteCurrency\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"单价\" prop=\"dnUnitRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"数量\" prop=\"dnAmount\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"单位\" prop=\"dnUnitCode\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"结算汇率\" prop=\"basicCurrencyRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"结算币种\" prop=\"dnCurrencyCode\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"税率\" prop=\"dutyRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"税金\" prop=\"dutyRate\" width=\"80\">\r\n              <template #default=\"scope\">\r\n                {{ (scope.row.dutyRate / 100) * scope.row.dnAmount * scope.row.dnUnitRate }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"含税小计\" prop=\"subtotal\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"开票项目名称\" prop=\"invoicingItem\" width=\"150\">\r\n              <template #default=\"scope\">\r\n                <treeselect v-model=\"scope.row.invoicingItem\"\r\n                            :class=\"{'disable-form': isDisabled()}\"\r\n                            :default-expand-level=\"1\"\r\n                            :disable-branch-nodes=\"true\"\r\n                            :disabled=\"isDisabled()\"\r\n                            :normalizer=\"invoicingItemNormalizer\"\r\n                            :options=\"invoicingItemOptions\"\r\n                            :show-count=\"true\"\r\n                            :z-index=\"9999\"\r\n                            append-to-body\r\n                            placeholder=\"开票项目名称\"\r\n                            searchable\r\n                />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"税收编码\" prop=\"taxCode\" width=\"100\"/>\r\n          </el-table>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 批量设置开票项目对话框 -->\r\n      <el-dialog\r\n        :visible.sync=\"batchInvoicingItemDialogVisible\"\r\n        append-to-body\r\n        title=\"批量设置开票项目\"\r\n        width=\"500px\"\r\n      >\r\n        <el-form :model=\"batchForm\" label-width=\"120px\">\r\n          <el-form-item label=\"开票项目名称\">\r\n            <treeselect\r\n              v-model=\"batchForm.invoicingItem\"\r\n              :default-expand-level=\"1\"\r\n              :disable-branch-nodes=\"true\"\r\n              :normalizer=\"invoicingItemNormalizer\"\r\n              :options=\"invoicingItemOptions\"\r\n              :show-count=\"true\"\r\n              placeholder=\"请选择开票项目名称\"\r\n              searchable\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"batchInvoicingItemDialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"confirmBatchSetInvoicingItem\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 操作按钮组 -->\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n        <el-col :span=\"3\">\r\n          <el-button icon=\"el-icon-check\" size=\"mini\" type=\"primary\">√默认对冲</el-button>\r\n          <div>已选总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">智选</el-button>\r\n          <div>未选总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">反选</el-button>\r\n          <div>全部总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button\r\n            :disabled=\"isDisabled()\"\r\n            size=\"mini\"\r\n            type=\"primary\" @click=\"submitForm\"\r\n          >\r\n            保存草稿\r\n          </el-button>\r\n          <div>{{ getName(formData.issuedStuffId) }}</div>\r\n          <div>{{ formData.issuedTime }}</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <div>\r\n            <el-button v-if=\"formData.applyStuffId === null\" size=\"mini\"\r\n                       type=\"primary\" @click=\"handleApplyInvoice\"\r\n            >{{ formData.saleBuy === \"sale\" ? \"申请开票\" : \"确认账单\" }}\r\n            </el-button>\r\n            <el-button v-show=\"formData.applyStuffId !== null\" :disabled=\"type === 'debitNote' || formData.auditStuffId !== null\" size=\"mini\"\r\n                       type=\"primary\" @click=\"handleInvoiceApplyCancel\"\r\n            >{{ formData.saleBuy === \"sale\" ? \"取消开票\" : \"取消账单\" }}\r\n            </el-button>\r\n          </div>\r\n\r\n          <div>{{ getName(formData.applyStuffId) }}</div>\r\n          <div>{{ formData.appliedTime }}</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <div>\r\n            <el-button\r\n              v-if=\"formData.auditStuffId === null\"\r\n              :disabled=\"type === 'debitNote' || formData.auditStuffId !== null\"\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAuditInvoice\"\r\n            >\r\n              财务审核\r\n            </el-button>\r\n            <el-button\r\n              v-else\r\n              :disabled=\"type === 'debitNote' || formData.auditStuffId === null\"\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleInvoiceAuditCancel\"\r\n            >\r\n              取消审核\r\n            </el-button>\r\n          </div>\r\n\r\n          <div>{{ getName(formData.auditStuffId) }}</div>\r\n          <div>{{ formData.auditTime }}</div>\r\n        </el-col>\r\n        <!--<el-col :span=\"3\">\r\n          <el-button\r\n            :disabled=\"type === 'debitNote' || formData.auditStatus !== '审核通过'\"\r\n            size=\"mini\"\r\n            type=\"success\"\r\n            @click=\"handleSendInvoice\"\r\n          >\r\n            发送开票\r\n          </el-button>\r\n          <div>{{ getName(formData.issuedStuffId) }}</div>\r\n          <div>{{ formData.issuedTime }}</div>\r\n        </el-col>-->\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"warning\">打印</el-button>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"info\">报税锁定</el-button>\r\n          <div>报税人+时间</div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport {getCompany} from \"@/api/system/company\"\r\nimport {listAccount} from \"@/api/system/account\"\r\nimport {updateVatinvoice} from \"@/api/system/vatInvoice\"\r\nimport FileUpload from \"@/components/FileUpload\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\nimport store from \"@/store\"\r\nimport {updateDebitNoteByInvoiceId} from \"@/api/system/debitnote\"\r\n\r\n// 防抖函数\r\nfunction debounce(fn, delay) {\r\n  let timer = null\r\n  return function () {\r\n    const context = this\r\n    const args = arguments\r\n    clearTimeout(timer)\r\n    timer = setTimeout(function () {\r\n      fn.apply(context, args)\r\n    }, delay)\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: \"VatinvoiceDialog\",\r\n  components: {\r\n    FileUpload,\r\n    Treeselect\r\n  },\r\n  props: {\r\n    // 是否显示对话框\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 标题\r\n    title: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 表单验证规则\r\n    rules: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 发票明细列表\r\n    invoiceItems: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 银行账户列表\r\n    bankAccountList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    type: {\r\n      type: String,\r\n      default: \"\"\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 内部对话框可见性状态\r\n      dialogVisible: false,\r\n      // 表单数据的副本\r\n      formData: {},\r\n      // 发票明细列表的副本\r\n      invoiceItemList: [],\r\n      // 防抖后的获取公司信息方法\r\n      debouncedFetchCompanyInfo: null,\r\n      // 公司银行账户列表\r\n      companyBankList: [],\r\n      // 开票项目选项数据\r\n      invoicingItemOptions: [\r\n        {\r\n          id: \"1\",\r\n          invoicingItemName: \"经纪代理服务\",\r\n          children: [\r\n            {id: \"1-1\", invoicingItemName: \"代理运费\"},\r\n            {id: \"1-2\", invoicingItemName: \"国际货物运输代理服务费\"},\r\n            {id: \"1-3\", invoicingItemName: \"代理港杂费\"},\r\n            {id: \"1-4\", invoicingItemName: \"国际货物运输代理服务\"},\r\n            {id: \"1-5\", invoicingItemName: \"代理报关服务费\"},\r\n            {id: \"1-6\", invoicingItemName: \"代理服务费\"},\r\n            {id: \"1-7\", invoicingItemName: \"代理报关费\"},\r\n            {id: \"1-8\", invoicingItemName: \"代理拖车费\"},\r\n            {id: \"1-9\", invoicingItemName: \"国际货物运输代理服务-代理运费\"},\r\n            {id: \"1-10\", invoicingItemName: \"代理国内运费\"},\r\n            {id: \"1-11\", invoicingItemName: \"国际货物运输代理海运费\"},\r\n            {id: \"1-13\", invoicingItemName: \"运输代理费\"},\r\n            {id: \"1-14\", invoicingItemName: \"货物运输代理服务费\"},\r\n            {id: \"1-15\", invoicingItemName: \"国际货物运输代理港杂费\"},\r\n            {id: \"1-16\", invoicingItemName: \"国际货物运输代理运费\"},\r\n            {id: \"1-17\", invoicingItemName: \"货物运输代理费\"},\r\n            {id: \"1-18\", invoicingItemName: \"国际货物运输代理费\"},\r\n            {id: \"1-19\", invoicingItemName: \"代理杂费\"},\r\n            {id: \"1-20\", invoicingItemName: \"代理文件费\"},\r\n            {id: \"1-21\", invoicingItemName: \"代理设备交接单费用\"},\r\n            {id: \"1-22\", invoicingItemName: \"代理舱单申报费\"},\r\n            {id: \"1-23\", invoicingItemName: \"代理操作费\"},\r\n            {id: \"1-24\", invoicingItemName: \"代理封条费\"},\r\n            {id: \"1-25\", invoicingItemName: \"代理码头操作费\"},\r\n            {id: \"1-26\", invoicingItemName: \"代理电放费\"},\r\n            {id: \"1-27\", invoicingItemName: \"代理核重费\"}\r\n          ]\r\n        }\r\n      ],\r\n      // 批量选择相关数据\r\n      selectedRows: [], // 选中的行数据\r\n      batchInvoicingItemDialogVisible: false, // 批量设置开票项目对话框可见性\r\n      batchForm: {\r\n        invoicingItem: null // 批量设置的开票项目\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 可用的银行账户列表：优先使用companyBankList，为空时使用传入的bankAccountList\r\n    availableBankList() {\r\n      return this.companyBankList.length > 0 ? this.companyBankList : this.bankAccountList\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 创建防抖版本的fetchCompanyInfo方法，设置300ms延迟\r\n    this.debouncedFetchCompanyInfo = debounce(this.fetchCompanyInfo, 300)\r\n  },\r\n  watch: {\r\n    visible: {\r\n      handler(val) {\r\n        this.dialogVisible = val\r\n        if (val) {\r\n          // 当对话框显示时，复制传入的数据\r\n          this.formData = JSON.parse(JSON.stringify(this.form))\r\n          this.invoiceItemList = JSON.parse(JSON.stringify(this.invoiceItems))\r\n\r\n          // 确保发票附件字段存在\r\n          if (!this.formData.invoiceAttachment) {\r\n            this.formData.invoiceAttachment = \"\"\r\n          }\r\n\r\n          // 如果所属公司已有值，自动填充相关信息\r\n          if (this.formData.invoiceBelongsTo) {\r\n            this.autoFillCompanyInfo(this.formData.invoiceBelongsTo)\r\n          }\r\n\r\n          // 初始化时计算发票金额\r\n          this.$nextTick(() => {\r\n            this.calculateInvoiceAmount()\r\n          })\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听 form prop 变化，当父组件更新发票数据时自动同步到子组件\r\n    form: {\r\n      handler(newVal) {\r\n        if (newVal && Object.keys(newVal).length > 0) {\r\n          // 当 form prop 变化时，更新内部的 formData\r\n          this.formData = JSON.parse(JSON.stringify(newVal))\r\n\r\n          // 如果所属公司已有值，自动填充相关信息\r\n          if (this.formData.invoiceBelongsTo) {\r\n            this.autoFillCompanyInfo(this.formData.invoiceBelongsTo)\r\n          }\r\n\r\n          // 重新计算发票金额\r\n          this.$nextTick(() => {\r\n            this.calculateInvoiceAmount()\r\n          })\r\n        }\r\n      },\r\n      deep: true\r\n    },\r\n    // 监听 invoiceItems prop 变化，当父组件更新费用明细时自动同步到子组件\r\n    // invoiceItems: {\r\n    //   handler(newVal) {\r\n    //     if (newVal && Array.isArray(newVal)) {\r\n    //       // 当 invoiceItems prop 变化时，更新内部的 invoiceItemList\r\n    //       this.invoiceItemList = JSON.parse(JSON.stringify(newVal))\r\n\r\n    //       // 重新计算发票金额\r\n    //       this.$nextTick(() => {\r\n    //         this.calculateInvoiceAmount()\r\n    //       })\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    // 监听对方公司ID变化\r\n    \"formData.cooperatorId\": {\r\n      handler(newVal, oldVal) {\r\n        // 只有当值真正变化时才触发查询\r\n        this.debouncedFetchCompanyInfo(newVal)\r\n      }\r\n    },\r\n    // 监听所属公司变化，自动填充我司发票抬头和税号\r\n    \"formData.invoiceBelongsTo\": {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          this.autoFillCompanyInfo(newVal)\r\n        }\r\n      }\r\n    },\r\n    // 发票金额自动根据发票汇率/发票币种/发票明细表的含税小计计算\r\n    \"formData.invoiceCurrencyCode\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      }\r\n    },\r\n    // 监听发票汇率变化\r\n    \"formData.invoiceExchangeRate\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      }\r\n    },\r\n    // 监听费用列表变化\r\n    \"formData.rsChargeList\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      },\r\n      deep: true\r\n    },\r\n    // 监听发票类型变化\r\n    \"formData.invoiceType\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      }\r\n    }\r\n  },\r\n  beforeMount() {\r\n    this.loadStaff()\r\n  },\r\n  methods: {\r\n    handleInvoiceAuditCancel() {\r\n      this.formData.auditStuffId = null\r\n      this.formData.auditTime = null\r\n      this.formData.auditStatus = null\r\n      // 清空发票流水号\r\n      this.formData.invoiceCodeNo = null\r\n      this.formData.rsChargeList.forEach(charge => {\r\n        charge.invoiceCodeNo = null\r\n      })\r\n      this.$emit(\"invoiceAuditCancel\", this.formData)\r\n    },\r\n    handleInvoiceApplyCancel() {\r\n      // 发票状态改为未开票\r\n      this.formData.applyStuffId = null\r\n      this.formData.appliedTime = null\r\n      this.formData.invoiceStatus = \"unissued\"\r\n\r\n      this.$emit(\"invoiceApplyCancel\", this.formData)\r\n    },\r\n    // 处理表格选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection\r\n    },\r\n\r\n    // 显示批量设置开票项目对话框\r\n    showBatchInvoicingItemDialog() {\r\n      this.batchForm.invoicingItem = null\r\n      this.batchInvoicingItemDialogVisible = true\r\n    },\r\n\r\n    // 确认批量设置开票项目\r\n    confirmBatchSetInvoicingItem() {\r\n      if (!this.batchForm.invoicingItem) {\r\n        this.$message.warning(\"请选择开票项目名称\")\r\n        return\r\n      }\r\n\r\n      // 为选中的行设置开票项目\r\n      this.selectedRows.forEach(row => {\r\n        row.invoicingItem = this.batchForm.invoicingItem\r\n      })\r\n\r\n      this.batchInvoicingItemDialogVisible = false\r\n      this.$message.success(`已为 ${this.selectedRows.length} 项设置开票项目`)\r\n\r\n      // 重新计算发票金额\r\n      this.calculateInvoiceAmount()\r\n    },\r\n\r\n    // 批量设置默认开票项目\r\n    batchSetDefaultInvoicingItem() {\r\n      // 根据费用名称智能设置默认开票项目\r\n      this.selectedRows.forEach(row => {\r\n        const chargeName = row.chargeName || \"\"\r\n\r\n        // 根据费用名称匹配默认开票项目\r\n        if (chargeName.includes(\"运费\") || chargeName.includes(\"运输\")) {\r\n          row.invoicingItem = \"代理运费\"\r\n        } else if (chargeName.includes(\"报关\")) {\r\n          row.invoicingItem = \"代理报关服务费\"\r\n        } else if (chargeName.includes(\"拖车\")) {\r\n          row.invoicingItem = \"代理拖车费\"\r\n        } else if (chargeName.includes(\"港杂\") || chargeName.includes(\"码头\")) {\r\n          row.invoicingItem = \"代理港杂费\"\r\n        } else if (chargeName.includes(\"文件\")) {\r\n          row.invoicingItem = \"代理文件费\"\r\n        } else if (chargeName.includes(\"操作\")) {\r\n          row.invoicingItem = \"代理操作费\"\r\n        } else {\r\n          // 默认设置为代理服务费\r\n          row.invoicingItem = \"代理服务费\"\r\n        }\r\n      })\r\n\r\n      this.$message.success(`已为 ${this.selectedRows.length} 项设置默认开票项目`)\r\n\r\n      // 重新计算发票金额\r\n      this.calculateInvoiceAmount()\r\n    },\r\n\r\n    // 批量清空开票项目\r\n    batchClearInvoicingItem() {\r\n      this.$confirm(\"确定要清空选中项的开票项目吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n        customClass: \"modal-confirm\"\r\n      }).then(() => {\r\n        this.selectedRows.forEach(row => {\r\n          row.invoicingItem = null\r\n        })\r\n\r\n        this.$message.success(`已清空 ${this.selectedRows.length} 项的开票项目`)\r\n\r\n        // 重新计算发票金额\r\n        this.calculateInvoiceAmount()\r\n      }).catch(() => {\r\n        // 用户取消操作\r\n      })\r\n    },\r\n\r\n    // 开票项目数据标准化函数\r\n    invoicingItemNormalizer(node) {\r\n      const normalized = {\r\n        id: node.invoicingItemName, // 使用invoicingItemName作为id\r\n        label: node.invoicingItemName,\r\n        invoicingItemName: node.invoicingItemName\r\n      }\r\n\r\n      if (node.children && node.children.length > 0) {\r\n        normalized.children = node.children.map(child => this.invoicingItemNormalizer(child))\r\n      }\r\n\r\n      return normalized\r\n    },\r\n\r\n    // 计算发票金额\r\n    calculateInvoiceAmount() {\r\n      // 检查必要的字段是否存在\r\n      if (!this.formData.rsChargeList || !Array.isArray(this.formData.rsChargeList)) {\r\n        this.formData.invoiceNetAmount = 0\r\n        return\r\n      }\r\n\r\n      // 计算费用明细的总金额和税额\r\n      // 税金\r\n      let vatAmount = this.formData.rsChargeList.reduce((acc, charge) => {\r\n        // 确保subtotal字段存在且为数字\r\n        const dutyRate = parseFloat(charge.dutyRate / 100) || 0\r\n        const vatAmount = dutyRate * (parseFloat(charge.dnAmount * charge.dnUnitRate) || 0)\r\n        return acc + vatAmount\r\n      }, 0)\r\n      // 含税金\r\n      let netAmount = this.formData.rsChargeList.reduce((acc, charge) => {\r\n        // 确保subtotal字段存在且为数字\r\n        const subtotal = parseFloat(charge.subtotal) || 0\r\n        return acc + subtotal\r\n      }, 0)\r\n      // 不含税金\r\n      let dnAmount = this.formData.rsChargeList.reduce((acc, charge) => {\r\n        // 确保subtotal字段存在且为数字\r\n        const subtotal = parseFloat(charge.dnAmount * charge.dnUnitRate) || 0\r\n        return acc + subtotal\r\n      }, 0)\r\n\r\n      // 保存应收/应付\r\n      if (this.formData.saleBuy === \"sale\") {\r\n        this.formData.dnSum = netAmount\r\n        this.formData.vatAmount = vatAmount\r\n      } else {\r\n        this.formData.cnSum = netAmount\r\n        this.formData.vatAmount = vatAmount\r\n      }\r\n\r\n      // 根据发票币种和汇率进行相应的计算\r\n      if (this.formData.invoiceCurrencyCode && this.formData.invoiceExchangeRate) {\r\n        const exchangeRate = parseFloat(this.formData.invoiceExchangeRate)\r\n        if (exchangeRate > 0) {\r\n          // 如果汇率大于0，则进行汇率转换\r\n          netAmount = netAmount * exchangeRate\r\n          dnAmount = dnAmount * exchangeRate\r\n          vatAmount = vatAmount * exchangeRate\r\n        }\r\n      }\r\n\r\n      // 如果是增值税专用发票，需要加上6%的税额\r\n      let taxAmount = 0\r\n      if (this.formData.invoiceType === \"增值税专用发票\") {\r\n        taxAmount = netAmount * 0.06\r\n        netAmount = netAmount + taxAmount\r\n      }\r\n\r\n      // 保留两位小数\r\n      // 不含税\r\n      this.formData.invoiceNetAmount = parseFloat(dnAmount.toFixed(2))\r\n      // 发票金额\r\n      this.formData.invoiceAmount = \"RMB\" + this.formatCurrency(dnAmount) + \" + \" + this.formatCurrency(vatAmount) + \" = \" + this.formatCurrency(netAmount)\r\n      // 结算金额\r\n      this.formData.settlementAmount = parseFloat(netAmount.toFixed(2))\r\n    },\r\n\r\n    // 获取发票金额占位符文本\r\n    getInvoiceAmountPlaceholder() {\r\n      if (!this.formData.rsChargeList || this.formData.rsChargeList.length === 0) {\r\n        return \"请先添加费用明细\"\r\n      }\r\n      if (!this.formData.invoiceCurrencyCode) {\r\n        return \"请选择发票币种\"\r\n      }\r\n      if (!this.formData.invoiceExchangeRate) {\r\n        return \"请输入发票汇率\"\r\n      }\r\n      return \"金额将自动计算\"\r\n    },\r\n\r\n    // 格式化货币显示\r\n    formatCurrency(amount) {\r\n      if (!amount && amount !== 0) return \"0.00\"\r\n      const num = parseFloat(amount)\r\n      if (isNaN(num)) return \"0.00\"\r\n      return num.toFixed(2)\r\n    },\r\n\r\n    // 提交表单\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          // 自动收集费用列表中的rct号，以逗号分隔填入相关订单号字段\r\n          this.collectRctNumbers()\r\n          this.$emit(\"submit\", this.formData)\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    handleCancel() {\r\n      this.$emit(\"cancel\")\r\n      this.handleClose()\r\n    },\r\n    // 关闭对话框\r\n    handleClose() {\r\n      this.$emit(\"update:visible\", false)\r\n    },\r\n    searchAvailableInvoice() {\r\n      console.log(\"检索可用发票\")\r\n    },\r\n\r\n    // 获取公司银行账户列表\r\n    fetchCompanyBankAccounts() {\r\n      // 检查是否有选择对方公司\r\n      if (!this.formData.cooperatorId) {\r\n        this.$message.warning(\"请先选择对方公司\")\r\n        return\r\n      }\r\n\r\n      // 调用API获取该公司的银行账户\r\n      listAccount({belongToCompany: this.formData.cooperatorId}).then(response => {\r\n        if (response.code === 200) {\r\n          this.companyBankList = response.rows || []\r\n          // 如果没有账户，显示提示\r\n          if (this.companyBankList.length === 0) {\r\n            this.$message.info(\"该公司没有银行账户记录\")\r\n          }\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取公司银行账户失败\", error)\r\n        this.$message.error(\"获取公司银行账户失败\")\r\n      })\r\n    },\r\n\r\n    // 处理银行账户选择变化\r\n    handleBankAccountChange(bankCode) {\r\n      // 根据选择的bankCode找到对应的银行账户信息\r\n      const selectedAccount = this.availableBankList.find(item => item.bankCode === bankCode)\r\n\r\n      if (selectedAccount) {\r\n        // 自动填充银行全称和银行账号\r\n        this.formData.cooperatorBankFullname = selectedAccount.bankBranchName || \"\"\r\n        this.formData.cooperatorBankAccount = selectedAccount.bankAccount || \"\"\r\n      }\r\n    },\r\n    // 获取公司信息\r\n    fetchCompanyInfo(companyId) {\r\n      if (!companyId) {\r\n        return\r\n      }\r\n      getCompany(companyId).then(response => {\r\n        if (response.code === 200) {\r\n          const companyInfo = response.data\r\n          // 更新表单中与对方公司相关的字段\r\n          this.formData.cooperatorCompanyTitle = companyInfo.companyLocalName || \"\"\r\n          this.formData.cooperatorVatSerialNo = companyInfo.taxNo || \"\"\r\n          this.formData.cooperatorShortName = companyInfo.companyShortName || \"\"\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取公司信息失败\", error)\r\n      })\r\n    },\r\n\r\n    // 自动填充我司发票抬头和税号\r\n    autoFillCompanyInfo(companyCode) {\r\n      // 根据所属公司代码(GZRS/HKRS/SZRS/GZCF)自动填充我司发票抬头和税号\r\n      const companyInfoMap = {\r\n        \"GZRS\": {\r\n          title: \"广州瑞旗国际货运代理有限公司\",\r\n          taxNo: \"91440101MA59UQXX7B\"\r\n        },\r\n        \"HKRS\": {\r\n          title: \"香港瑞旗国际货运代理有限公司\",\r\n          taxNo: \"HK12345678\"\r\n        },\r\n        \"SZRS\": {\r\n          title: \"深圳市瑞旗国际货运代理有限公司\",\r\n          taxNo: \"91440300MA5G9UB57Q\"\r\n        },\r\n        \"GZCF\": {\r\n          title: \"广州正泽国际货运代理有限公司\",\r\n          taxNo: \"91440101MA9XRGLH0F\"\r\n        }\r\n      }\r\n\r\n      // 获取对应公司的信息\r\n      const companyInfo = companyInfoMap[companyCode]\r\n\r\n      // 如果找到对应的公司信息，则填充表单\r\n      if (companyInfo) {\r\n        this.formData.richCompanyTitle = companyInfo.title\r\n        this.formData.richVatSerialNo = companyInfo.taxNo\r\n      }\r\n    },\r\n\r\n    // 判断表单项是否禁用\r\n    isDisabled() {\r\n      // 根据以下条件判断表单是否应该禁用：\r\n      // 1. 如果发票状态为已开票\r\n      if (this.type === \"debitNote\" && this.formData.invoiceStatus === \"applied\") {\r\n        return true\r\n      }\r\n\r\n      // 2. 如果报税已锁定\r\n      if (this.formData.taxLocked === \"1\") {\r\n        return true\r\n      }\r\n\r\n      // 3. 如果已支付\r\n      // if (this.formData.actualPayDate) {\r\n      //   return true\r\n      // }\r\n\r\n      // 如果发票已审核，则不可编辑\r\n      if (this.formData.auditStatus === \"审核通过\") {\r\n        return true\r\n      }\r\n\r\n      // 如果没有禁用条件，则表单可编辑\r\n      return false\r\n    },\r\n    handleRichBankCodeChange(row) {\r\n      this.formData.richBankFullname = row.bankBranchName\r\n      this.formData.richBankAccount = row.bankAccount\r\n    },\r\n    // 获取发票状态类型\r\n    getInvoiceStatusType(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"info\",\r\n        \"issued\": \"success\",\r\n        \"applied\": \"warning\",\r\n        \"canceled\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n    // 获取发票状态文本\r\n    getInvoiceStatusText(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"未开票\",\r\n        \"issued\": \"已开票\",\r\n        \"applied\": \"已申请\",\r\n        \"confirmed\": \"已审核\",\r\n        \"canceled\": \"已作废\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    },\r\n    // 申请开票\r\n    async handleApplyInvoice() {\r\n      try {\r\n        // 获取当前用户信息\r\n        const currentUser = this.$store.state.user\r\n        const currentTime = new Date().toISOString()\r\n\r\n        // 更新发票状态为已申请\r\n        const updateData = {\r\n          ...this.formData,\r\n          invoiceStatus: \"applied\",\r\n          applyStuffId: currentUser.sid, // 开票人ID\r\n          appliedTime: currentTime // 开票时间\r\n        }\r\n\r\n        // 调用API更新发票状态\r\n        const response = await updateVatinvoice(updateData)\r\n\r\n        if (response.code === 200) {\r\n          // 更新本地数据\r\n          this.formData.invoiceStatus = \"applied\"\r\n          this.formData.applyStuffId = currentUser.sid\r\n          this.formData.appliedTime = currentTime\r\n\r\n          this.$message.success(\"申请开票成功\")\r\n\r\n          // 通知父组件数据变化\r\n          this.$emit(\"applyInvoice\", this.formData)\r\n        } else {\r\n          this.$message.error(response.msg || \"申请开票失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"申请开票失败:\", error)\r\n        this.$message.error(\"申请开票失败，请重试\")\r\n      }\r\n    },\r\n    // 信息审核\r\n    async handleAuditInvoice() {\r\n      // 获取当前用户信息\r\n      const currentUser = this.$store.state.user\r\n      const currentTime = new Date().toISOString()\r\n\r\n      // 更新审核信息\r\n      this.formData = {\r\n        ...this.formData,\r\n        invoiceStatus: \"confirmed\",\r\n        auditStuffId: currentUser.sid, // 审核人ID\r\n        auditTime: currentTime, // 审核时间\r\n        auditStatus: \"审核通过\" // 审核状态\r\n      }\r\n\r\n      // 通知父组件数据变化\r\n      this.$emit(\"auditInvoice\", this.formData)\r\n    },\r\n    // 发送开票\r\n    async handleSendInvoice() {\r\n      try {\r\n        // 检查审核状态，只有审核通过的发票才能开票\r\n        if (this.formData.auditStatus !== \"审核通过\") {\r\n          this.$message.error(\"只有审核通过的发票才能进行开票操作\")\r\n          return\r\n        }\r\n\r\n        // 获取当前用户信息\r\n        const currentUser = this.$store.state.user\r\n        const currentTime = new Date().toISOString()\r\n\r\n        // 更新开票信息\r\n        const updateData = {\r\n          ...this.formData,\r\n          invoiceStatus: \"issued\", // 发票状态为已开票\r\n          issuedStuffId: currentUser.sid, // 开票人ID\r\n          issuedTime: currentTime // 开票时间\r\n        }\r\n\r\n        // 调用API更新发票开票信息\r\n        const response = await updateVatinvoice(updateData)\r\n\r\n        if (response.code === 200) {\r\n          // 更新本地数据\r\n          this.formData.invoiceStatus = \"issued\"\r\n          this.formData.issuedStuffId = currentUser.sid\r\n          this.formData.issuedTime = currentTime\r\n\r\n          this.$message.success(\"发送开票成功\")\r\n\r\n          // 通知父组件数据变化\r\n          this.$emit(\"sendInvoice\", this.formData)\r\n        } else {\r\n          this.$message.error(response.msg || \"发送开票失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"发送开票失败:\", error)\r\n        this.$message.error(\"发送开票失败，请重试\")\r\n      }\r\n    }\r\n    ,\r\n    // 判断是否可以申请开票\r\n    canApplyInvoice() {\r\n      // 检查是否有发票ID\r\n      if (!this.formData.invoiceId) {\r\n        return false\r\n      }\r\n\r\n      // 检查发票状态\r\n      if (this.formData.invoiceStatus === \"issued\") {\r\n        return false // 已开票不能再次申请\r\n      }\r\n\r\n      if (this.formData.invoiceStatus === \"applied\") {\r\n        return false // 已申请不能重复申请\r\n      }\r\n\r\n      // 检查是否已报税锁定\r\n      if (this.formData.taxLocked === \"1\") {\r\n        return false\r\n      }\r\n\r\n      // 检查是否已支付（如果需要的话）\r\n      // if (this.formData.actualPayDate) {\r\n      //   return false\r\n      // }\r\n\r\n      return true\r\n    }\r\n    ,\r\n    // 获取申请信息\r\n    getApplyInfo() {\r\n      if (this.formData.invoiceStatus === \"applied\") {\r\n        // 如果已申请开票，显示申请人+时间\r\n        const currentUser = this.$store.state.user\r\n        let applyTime = \"\"\r\n\r\n        if (this.formData.issuedTime) {\r\n          try {\r\n            applyTime = new Date(this.formData.issuedTime).toLocaleString(\"zh-CN\", {\r\n              year: \"numeric\",\r\n              month: \"2-digit\",\r\n              day: \"2-digit\",\r\n              hour: \"2-digit\",\r\n              minute: \"2-digit\"\r\n            })\r\n          } catch (e) {\r\n            applyTime = \"时间格式错误\"\r\n          }\r\n        }\r\n\r\n        const userName = this.getName(currentUser.sid) || \"未知用户\"\r\n        return `${userName}${applyTime}`\r\n      }\r\n      if (this.formData.invoiceStatus === \"issued\") {\r\n        return \"已开票\"\r\n      }\r\n      if (this.formData.taxLocked === \"1\") {\r\n        return \"已报税锁定\"\r\n      }\r\n      if (this.formData.actualPayDate) {\r\n        return \"已支付\"\r\n      }\r\n      return \"未申请\"\r\n    }\r\n    ,\r\n    getName(id) {\r\n      if (id) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff) {\r\n          return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n        } else {\r\n          return \"\"\r\n        }\r\n      } else {\r\n        return \"\"\r\n      }\r\n    }\r\n    ,\r\n    loadStaff() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === \"业务部\")\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === \"业务部\")\r\n      }\r\n    }\r\n    ,\r\n\r\n    // 自动收集费用列表中的rct号，以逗号分隔填入相关订单号字段\r\n    collectRctNumbers() {\r\n      if (!this.formData.rsChargeList || !Array.isArray(this.formData.rsChargeList)) {\r\n        return\r\n      }\r\n\r\n      // 收集所有不重复的rct号\r\n      const rctNumbers = new Set()\r\n      this.formData.rsChargeList.forEach(charge => {\r\n        if (charge.sqdRctNo && charge.sqdRctNo.trim()) {\r\n          rctNumbers.add(charge.sqdRctNo.trim())\r\n        }\r\n      })\r\n\r\n      // 将rct号转换为数组并排序，然后以逗号分隔\r\n      const rctArray = Array.from(rctNumbers).sort()\r\n      this.formData.relatedOrderNo = rctArray.join(\",\")\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.yellow-bg {\r\n  background-color: #fffbe6;\r\n}\r\n\r\n.disable-form {\r\n  background-color: #f5f7fa;\r\n  border-color: #e4e7ed;\r\n  color: #c0c4cc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.uploaded-file-preview {\r\n  margin-top: 10px;\r\n  padding: 10px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.preview-title {\r\n  font-weight: bold;\r\n  color: #495057;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.file-links {\r\n  display: flex;\r\n  gap: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.file-links .el-link {\r\n  font-size: 12px;\r\n}\r\n\r\n/* treeselect组件样式 */\r\n:deep(.vue-treeselect__menu) {\r\n  z-index: 9999 !important;\r\n  position: fixed !important;\r\n}\r\n\r\n:deep(.vue-treeselect__menu-container) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n:deep(.vue-treeselect__dropdown) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n/* 确保下拉框在表格之上 */\r\n:deep(.vue-treeselect__menu-arrow) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n:deep(.vue-treeselect__list) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n/* 金额公式样式 */\r\n.amount-formula {\r\n  margin-top: 5px;\r\n  padding: 5px 8px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #409eff;\r\n}\r\n\r\n.amount-formula small {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.text-muted {\r\n  color: #909399 !important;\r\n}\r\n</style>\r\n"]}]}