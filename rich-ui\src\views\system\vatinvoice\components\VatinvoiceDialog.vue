<template>
  <el-dialog
    v-dialogDrag
    v-dialogDragWidth
    :close-on-click-modal="false"
    :modal-append-to-body="false"
    :title="title"
    :visible.sync="dialogVisible"
    append-to-body
    modal
    width="80%"
    @close="handleClose"
  >
    <el-form ref="form" :model="formData" :rules="rules" class="edit" label-width="80px" size="mini">
      <!-- 第一行 - 基本信息 -->
      <el-row :gutter="10">
        <el-col :span="4">
          <el-form-item label="发票流水号">
            <el-input v-model="formData.invoiceCodeNo" :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                      placeholder="发票流水号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="进销标志">
            <el-select v-model="formData.saleBuy" :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                       placeholder="进销标志"
                       style="width: 100%"
            >
              <el-option label="销项" value="sale"/>
              <el-option label="进项" value="buy"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="发票性质">
            <!-- 主营业务收入/非主营收入/营业外收入/成本/费用 -->
            <el-select v-model="formData.taxClass" :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                       placeholder="发票性质"
                       style="width: 100%"
            >
              <el-option label="主营业务收入" value="主营业务收入"/>
              <el-option label="非主营业务收入" value="非主营业务收入"/>
              <el-option label="营业外收入" value="营业外收入"/>
              <el-option label="成本" value="成本"/>
              <el-option label="费用" value="费用"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="发票类型">
            <el-select v-model="formData.invoiceType" :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                       placeholder="发票类型" style="width: 100%"
            >
              <el-option label="增值税专用发票" value="增值税专用发票"/>
              <el-option label="普通发票" value="普通发票"/>
              <el-option label="收据" value="收据"/>
              <el-option label="无票收支" value="无票收支"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="接收合开">
            <el-row>
              <el-col :offset="1" :span="8">
                <el-checkbox v-model="formData.mergeInvoice" :class="{'disable-form': isDisabled()}"
                             :disabled="isDisabled()" false-label="0"
                             true-label="1"
                >
                </el-checkbox>
              </el-col>
              <el-col :span="12">
                <el-tag :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                        :type="getInvoiceStatusType(formData.invoiceStatus)"
                >{{ getInvoiceStatusText(formData.invoiceStatus) }}
                </el-tag>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="发票号码">
            <el-row>
              <el-col :span="18">
                <el-input v-model="formData.invoiceOfficalNo" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="发票号码"
                />
              </el-col>
              <el-col :span="6">
                <el-popover
                  placement="top-start"
                  title="发票附件管理"
                  trigger="hover"
                  width="300"
                >
                  <div>
                    <file-upload
                      :class="isDisabled()?'disable-form':''"
                      :file-type="['pdf']"
                      :is-tip-flex="true"
                      :value="formData.invoiceAttachment"
                      @input="formData.invoiceAttachment=$event"
                    />
                  </div>
                  <template #reference>
                    <el-button size="mini" type="text">
                      [发票]
                    </el-button>
                  </template>
                </el-popover>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行 - 公司和账户信息 -->
      <el-row :gutter="10">
        <el-col :span="16">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="所属公司">
                <el-input v-model="formData.invoiceBelongsTo" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="所属公司"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="我司账户">
                <tree-select :flat="false" :multiple="false"
                             :pass="formData.richBankCode" :placeholder="'银行账户'"
                             :type="'companyAccount'" @return="formData.richBankCode=$event"
                             :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                             @returnData="handleRichBankCodeChange"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="对方公司">
                <company-select :load-options="companyList"
                                :multiple="false" :no-parent="true"
                                :pass="formData.cooperatorId" :placeholder="''"
                                @return="formData.cooperatorId=$event"
                                :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="对方账户">
                <el-select v-model="formData.cooperatorBankCode" :class="{'disable-form': isDisabled()}"
                           :disabled="isDisabled()"
                           placeholder="对方账户" style="width: 100%"
                           @change="handleBankAccountChange" @click.native="fetchCompanyBankAccounts"
                >
                  <el-option v-for="item in availableBankList" :key="item.bankAccId"
                             :label="item.bankAccCode+ '('+item.bankAccount+')'" :value="item.bankAccCode"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="发票抬头">
                <el-input v-model="formData.richCompanyTitle" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="我司发票抬头"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发票抬头">
                <el-input v-model="formData.cooperatorCompanyTitle" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="对方发票抬头"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>

        <el-col :span="8">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-input v-model="formData.opRemark" :minrows="3" :rows="2"
                        placeholder="开票要求" type="textarea"
                        :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
              />
            </el-col>
            <el-col :span="12">
              <el-input v-model="formData.relatedOrderNo" :minrows="3" :rows="2" placeholder="相关订单号"
                        type="textarea"
                        :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
              />
            </el-col>
          </el-row>
        </el-col>
      </el-row>

      <!-- 第四行 - 税号信息 -->
      <el-row :gutter="10">
        <el-col :span="16">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="税号">
                <el-input v-model="formData.richVatSerialNo" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="我司纳税人识别号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="税号">
                <el-input v-model="formData.cooperatorVatSerialNo" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()" placeholder="对方纳税人识别号"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="银行全称">
                <el-input v-model="formData.richBankFullname" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="我司银行全称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="银行全称">
                <el-input v-model="formData.cooperatorBankFullname" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="对方银行全称"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="银行账号">
                <el-input v-model="formData.richBankAccount" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="我司账号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="银行账号">
                <el-input v-model="formData.cooperatorBankAccount" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="对方账号"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="结算币种">
                <tree-select :class="{'disable-form': isDisabled()}"
                             :disabled="isDisabled()" :pass="formData.settlementCurrencyCode"
                             :placeholder="'币种'" :type="'currency'"
                             style="width: 100%" @return="formData.settlementCurrencyCode=$event"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="结算金额">
                <el-input v-model="formData.settlementAmount" class="disable-form"
                          disabled placeholder="结算金额"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item label="发票汇率">
                <el-input v-model="formData.invoiceExchangeRate" :class="{'disable-form': isDisabled()}"
                          :disabled="isDisabled()"
                          placeholder="发票汇率"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发票金额">
                <el-input v-model="formData.invoiceAmount" :placeholder="getInvoiceAmountPlaceholder()"
                          class="disable-form"
                          disabled
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>

        <el-col :span="4">
          <el-input v-model="formData.invoiceRemark" :class="{'disable-form': isDisabled()}" :disabled="isDisabled()"
                    :minrows="6" :rows="6"
                    placeholder="税务发票备注栏" type="textarea"
          />
        </el-col>

        <el-col :span="4">
          <el-col>
            <el-form-item label="期望支付日">
              <el-date-picker v-model="formData.expectedPayDate"
                              clearable
                              placeholder="期望支付日期"
                              style="width: 100%;"
                              type="date"
                              value-format="yyyy-MM-dd"
                              :class="{'disable-form': isDisabled()}"
                              :disabled="isDisabled()"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="批复支付日">
              <el-date-picker v-model="formData.approvedPayDate"
                              clearable
                              placeholder="批复支付日期"
                              style="width: 100%;"
                              type="date"
                              value-format="yyyy-MM-dd"
                              :class="{'disable-form': isDisabled()}"
                              :disabled="isDisabled()"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="实际支付日">
              <el-date-picker v-model="formData.actualPayDate"
                              clearable
                              placeholder="实际支付日期"
                              style="width: 100%;"
                              type="date"
                              value-format="yyyy-MM-dd"
                              :class="{'disable-form': isDisabled()}"
                              :disabled="isDisabled()"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="报税月份">
              <el-input v-model="formData.belongsToMonth"
                        :class="{'disable-form': isDisabled() || type === 'debitNote'}"
                        :disabled="isDisabled() || type === 'debitNote'"
                        class="yellow-bg" placeholder="2025/7/31"
              />
            </el-form-item>
          </el-col>
        </el-col>
      </el-row>


      <el-divider></el-divider>

      <!-- 发票明细表格 -->
      <el-row :gutter="10">
        <el-col :span="24">
          <!-- 批量操作工具栏 -->
          <div style="margin-bottom: 10px; display: flex; align-items: center; gap: 10px;">
            <el-button
              :disabled="isDisabled() || !selectedRows.length"
              icon="el-icon-edit"
              size="mini"
              type="primary"
              @click="showBatchInvoicingItemDialog"
            >
              批量设置开票项目
            </el-button>
            <el-button
              :disabled="isDisabled() || !selectedRows.length"
              icon="el-icon-check"
              size="mini"
              type="success"
              @click="batchSetDefaultInvoicingItem"
            >
              批量设置默认项目
            </el-button>
            <el-button
              :disabled="isDisabled() || !selectedRows.length"
              icon="el-icon-delete"
              size="mini"
              type="warning"
              @click="batchClearInvoicingItem"
            >
              批量清空开票项目
            </el-button>
            <span style="margin-left: 20px; color: #606266;">
              已选择 {{ selectedRows.length }} 项
            </span>
          </div>

          <el-table
            ref="chargeTable"
            :data="formData.rsChargeList"
            border
            size="mini"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column align="center" type="selection" width="35"/>
            <el-table-column align="center" label="账单编号" prop="sqdInvoiceCodeNo" width="100"/>
            <el-table-column align="center" label="RCT号" prop="sqdRctNo"/>
            <el-table-column align="center" label="所属服务" prop="serviceLocalName" width="100">
              <template #default="scope">
                {{ scope.row.sqdServiceTypeId == 0 ? "客户应收" : scope.row.serviceLocalName }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="费用名称" prop="chargeName" width="100"/>
            <el-table-column align="center" label="备注" prop="chargeRemark" width="100"/>
            <el-table-column align="center" label="收付标志" prop="isReceivingOrPaying" width="80">
              <template #default="scope">{{ scope.row.isReceivingOrPaying == 0 ? "应收" : "应付" }}</template>
            </el-table-column>
            <el-table-column align="center" label="报价币种" prop="quoteCurrency" width="80"/>
            <el-table-column align="center" label="单价" prop="dnUnitRate" width="80"/>
            <el-table-column align="center" label="数量" prop="dnAmount" width="80"/>
            <el-table-column align="center" label="单位" prop="dnUnitCode" width="80"/>
            <el-table-column align="center" label="结算汇率" prop="basicCurrencyRate" width="80"/>
            <el-table-column align="center" label="结算币种" prop="dnCurrencyCode" width="80"/>
            <el-table-column align="center" label="税率" prop="dutyRate" width="80"/>
            <el-table-column align="center" label="税金" prop="dutyRate" width="80">
              <template #default="scope">
                {{ (scope.row.dutyRate / 100) * scope.row.dnAmount * scope.row.dnUnitRate }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="含税小计" prop="subtotal" width="100"/>
            <el-table-column align="center" label="开票项目名称" prop="invoicingItem" width="150">
              <template #default="scope">
                <treeselect v-model="scope.row.invoicingItem"
                            :class="{'disable-form': isDisabled()}"
                            :default-expand-level="1"
                            :disable-branch-nodes="true"
                            :disabled="isDisabled()"
                            :normalizer="invoicingItemNormalizer"
                            :options="invoicingItemOptions"
                            :show-count="true"
                            :z-index="9999"
                            append-to-body
                            placeholder="开票项目名称"
                            searchable
                />
              </template>
            </el-table-column>
            <el-table-column align="center" label="税收编码" prop="taxCode" width="100"/>
          </el-table>
        </el-col>
      </el-row>

      <!-- 批量设置开票项目对话框 -->
      <el-dialog
        :visible.sync="batchInvoicingItemDialogVisible"
        append-to-body
        title="批量设置开票项目"
        width="500px"
      >
        <el-form :model="batchForm" label-width="120px">
          <el-form-item label="开票项目名称">
            <treeselect
              v-model="batchForm.invoicingItem"
              :default-expand-level="1"
              :disable-branch-nodes="true"
              :normalizer="invoicingItemNormalizer"
              :options="invoicingItemOptions"
              :show-count="true"
              placeholder="请选择开票项目名称"
              searchable
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="batchInvoicingItemDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmBatchSetInvoicingItem">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 操作按钮组 -->
      <el-row :gutter="10" style="margin-top: 10px;">
        <el-col :span="3">
          <el-button icon="el-icon-check" size="mini" type="primary">√默认对冲</el-button>
          <div>已选总计:</div>
        </el-col>
        <el-col :span="3">
          <el-button size="mini" type="primary">智选</el-button>
          <div>未选总计:</div>
        </el-col>
        <el-col :span="3">
          <el-button size="mini" type="primary">反选</el-button>
          <div>全部总计:</div>
        </el-col>
        <el-col :span="3">
          <el-button
            :disabled="isDisabled()"
            size="mini"
            type="primary" @click="submitForm"
          >
            保存草稿
          </el-button>
          <div>{{ getName(formData.issuedStuffId) }}</div>
          <div>{{ formData.issuedTime }}</div>
        </el-col>
        <el-col :span="3">
          <div>
            <el-button v-if="formData.applyStuffId === null" size="mini"
                       type="primary" @click="handleApplyInvoice"
            >{{ formData.saleBuy === "sale" ? "申请开票" : "确认账单" }}
            </el-button>
            <el-button v-show="formData.applyStuffId !== null" :disabled="type === 'debitNote' || formData.auditStuffId !== null" size="mini"
                       type="primary" @click="handleInvoiceApplyCancel"
            >{{ formData.saleBuy === "sale" ? "取消开票" : "取消账单" }}
            </el-button>
          </div>

          <div>{{ getName(formData.applyStuffId) }}</div>
          <div>{{ formData.appliedTime }}</div>
        </el-col>
        <el-col :span="3">
          <div>
            <el-button
              v-if="formData.auditStuffId === null"
              :disabled="type === 'debitNote' || formData.auditStuffId !== null"
              size="mini"
              type="primary"
              @click="handleAuditInvoice"
            >
              财务审核
            </el-button>
            <el-button
              v-else
              :disabled="type === 'debitNote' || formData.auditStuffId === null"
              size="mini"
              type="primary"
              @click="handleInvoiceAuditCancel"
            >
              取消审核
            </el-button>
          </div>

          <div>{{ getName(formData.auditStuffId) }}</div>
          <div>{{ formData.auditTime }}</div>
        </el-col>
        <!--<el-col :span="3">
          <el-button
            :disabled="type === 'debitNote' || formData.auditStatus !== '审核通过'"
            size="mini"
            type="success"
            @click="handleSendInvoice"
          >
            发送开票
          </el-button>
          <div>{{ getName(formData.issuedStuffId) }}</div>
          <div>{{ formData.issuedTime }}</div>
        </el-col>-->
        <el-col :span="3">
          <el-button size="mini" type="warning">打印</el-button>
        </el-col>
        <el-col :span="3">
          <el-button size="mini" type="info">报税锁定</el-button>
          <div>报税人+时间</div>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
import {getCompany} from "@/api/system/company"
import {listAccount} from "@/api/system/account"
import {updateVatinvoice} from "@/api/system/vatInvoice"
import FileUpload from "@/components/FileUpload"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import store from "@/store"
import {updateDebitNoteByInvoiceId} from "@/api/system/debitnote"

// 防抖函数
function debounce(fn, delay) {
  let timer = null
  return function () {
    const context = this
    const args = arguments
    clearTimeout(timer)
    timer = setTimeout(function () {
      fn.apply(context, args)
    }, delay)
  }
}

export default {
  name: "VatinvoiceDialog",
  components: {
    FileUpload,
    Treeselect
  },
  props: {
    // 是否显示对话框
    visible: {
      type: Boolean,
      default: false
    },
    // 标题
    title: {
      type: String,
      default: ""
    },
    // 表单数据
    form: {
      type: Object,
      default: () => ({})
    },
    // 表单验证规则
    rules: {
      type: Object,
      default: () => ({})
    },
    // 发票明细列表
    invoiceItems: {
      type: Array,
      default: () => []
    },
    companyList: {
      type: Array,
      default: () => []
    },
    // 银行账户列表
    bankAccountList: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      // 内部对话框可见性状态
      dialogVisible: false,
      // 表单数据的副本
      formData: {},
      // 发票明细列表的副本
      invoiceItemList: [],
      // 防抖后的获取公司信息方法
      debouncedFetchCompanyInfo: null,
      // 公司银行账户列表
      companyBankList: [],
      // 开票项目选项数据
      invoicingItemOptions: [
        {
          id: "1",
          invoicingItemName: "经纪代理服务",
          children: [
            {id: "1-1", invoicingItemName: "代理运费"},
            {id: "1-2", invoicingItemName: "国际货物运输代理服务费"},
            {id: "1-3", invoicingItemName: "代理港杂费"},
            {id: "1-4", invoicingItemName: "国际货物运输代理服务"},
            {id: "1-5", invoicingItemName: "代理报关服务费"},
            {id: "1-6", invoicingItemName: "代理服务费"},
            {id: "1-7", invoicingItemName: "代理报关费"},
            {id: "1-8", invoicingItemName: "代理拖车费"},
            {id: "1-9", invoicingItemName: "国际货物运输代理服务-代理运费"},
            {id: "1-10", invoicingItemName: "代理国内运费"},
            {id: "1-11", invoicingItemName: "国际货物运输代理海运费"},
            {id: "1-13", invoicingItemName: "运输代理费"},
            {id: "1-14", invoicingItemName: "货物运输代理服务费"},
            {id: "1-15", invoicingItemName: "国际货物运输代理港杂费"},
            {id: "1-16", invoicingItemName: "国际货物运输代理运费"},
            {id: "1-17", invoicingItemName: "货物运输代理费"},
            {id: "1-18", invoicingItemName: "国际货物运输代理费"},
            {id: "1-19", invoicingItemName: "代理杂费"},
            {id: "1-20", invoicingItemName: "代理文件费"},
            {id: "1-21", invoicingItemName: "代理设备交接单费用"},
            {id: "1-22", invoicingItemName: "代理舱单申报费"},
            {id: "1-23", invoicingItemName: "代理操作费"},
            {id: "1-24", invoicingItemName: "代理封条费"},
            {id: "1-25", invoicingItemName: "代理码头操作费"},
            {id: "1-26", invoicingItemName: "代理电放费"},
            {id: "1-27", invoicingItemName: "代理核重费"}
          ]
        }
      ],
      // 批量选择相关数据
      selectedRows: [], // 选中的行数据
      batchInvoicingItemDialogVisible: false, // 批量设置开票项目对话框可见性
      batchForm: {
        invoicingItem: null // 批量设置的开票项目
      }
    }
  },
  computed: {
    // 可用的银行账户列表：优先使用companyBankList，为空时使用传入的bankAccountList
    availableBankList() {
      return this.companyBankList.length > 0 ? this.companyBankList : this.bankAccountList
    }
  },

  created() {
    // 创建防抖版本的fetchCompanyInfo方法，设置300ms延迟
    this.debouncedFetchCompanyInfo = debounce(this.fetchCompanyInfo, 300)
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          // 当对话框显示时，复制传入的数据
          this.formData = JSON.parse(JSON.stringify(this.form))
          this.invoiceItemList = JSON.parse(JSON.stringify(this.invoiceItems))

          // 确保发票附件字段存在
          if (!this.formData.invoiceAttachment) {
            this.formData.invoiceAttachment = ""
          }

          // 如果所属公司已有值，自动填充相关信息
          if (this.formData.invoiceBelongsTo) {
            this.autoFillCompanyInfo(this.formData.invoiceBelongsTo)
          }

          // 初始化时计算发票金额
          this.$nextTick(() => {
            this.calculateInvoiceAmount()
          })
        }
      },
      immediate: true
    },
    // 监听 form prop 变化，当父组件更新发票数据时自动同步到子组件
    form: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          // 当 form prop 变化时，更新内部的 formData
          this.formData = JSON.parse(JSON.stringify(newVal))

          // 如果所属公司已有值，自动填充相关信息
          if (this.formData.invoiceBelongsTo) {
            this.autoFillCompanyInfo(this.formData.invoiceBelongsTo)
          }

          // 重新计算发票金额
          this.$nextTick(() => {
            this.calculateInvoiceAmount()
          })
        }
      },
      deep: true
    },
    // 监听 invoiceItems prop 变化，当父组件更新费用明细时自动同步到子组件
    // invoiceItems: {
    //   handler(newVal) {
    //     if (newVal && Array.isArray(newVal)) {
    //       // 当 invoiceItems prop 变化时，更新内部的 invoiceItemList
    //       this.invoiceItemList = JSON.parse(JSON.stringify(newVal))

    //       // 重新计算发票金额
    //       this.$nextTick(() => {
    //         this.calculateInvoiceAmount()
    //       })
    //     }
    //   },
    //   deep: true
    // },
    // 监听对方公司ID变化
    "formData.cooperatorId": {
      handler(newVal, oldVal) {
        // 只有当值真正变化时才触发查询
        this.debouncedFetchCompanyInfo(newVal)
      }
    },
    // 监听所属公司变化，自动填充我司发票抬头和税号
    "formData.invoiceBelongsTo": {
      handler(newVal) {
        if (newVal) {
          this.autoFillCompanyInfo(newVal)
        }
      }
    },
    // 发票金额自动根据发票汇率/发票币种/发票明细表的含税小计计算
    "formData.invoiceCurrencyCode": {
      handler(newVal) {
        this.calculateInvoiceAmount()
      }
    },
    // 监听发票汇率变化
    "formData.invoiceExchangeRate": {
      handler(newVal) {
        this.calculateInvoiceAmount()
      }
    },
    // 监听费用列表变化
    "formData.rsChargeList": {
      handler(newVal) {
        this.calculateInvoiceAmount()
      },
      deep: true
    },
    // 监听发票类型变化
    "formData.invoiceType": {
      handler(newVal) {
        this.calculateInvoiceAmount()
      }
    }
  },
  beforeMount() {
    this.loadStaff()
  },
  methods: {
    handleInvoiceAuditCancel() {
      this.formData.auditStuffId = null
      this.formData.auditTime = null
      this.formData.auditStatus = null
      // 清空发票流水号
      this.formData.invoiceCodeNo = null
      this.formData.rsChargeList.forEach(charge => {
        charge.invoiceCodeNo = null
      })
      this.$emit("invoiceAuditCancel", this.formData)
    },
    handleInvoiceApplyCancel() {
      // 发票状态改为未开票
      this.formData.applyStuffId = null
      this.formData.appliedTime = null
      this.formData.invoiceStatus = "unissued"

      this.$emit("invoiceApplyCancel", this.formData)
    },
    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 显示批量设置开票项目对话框
    showBatchInvoicingItemDialog() {
      this.batchForm.invoicingItem = null
      this.batchInvoicingItemDialogVisible = true
    },

    // 确认批量设置开票项目
    confirmBatchSetInvoicingItem() {
      if (!this.batchForm.invoicingItem) {
        this.$message.warning("请选择开票项目名称")
        return
      }

      // 为选中的行设置开票项目
      this.selectedRows.forEach(row => {
        row.invoicingItem = this.batchForm.invoicingItem
      })

      this.batchInvoicingItemDialogVisible = false
      this.$message.success(`已为 ${this.selectedRows.length} 项设置开票项目`)

      // 重新计算发票金额
      this.calculateInvoiceAmount()
    },

    // 批量设置默认开票项目
    batchSetDefaultInvoicingItem() {
      // 根据费用名称智能设置默认开票项目
      this.selectedRows.forEach(row => {
        const chargeName = row.chargeName || ""

        // 根据费用名称匹配默认开票项目
        if (chargeName.includes("运费") || chargeName.includes("运输")) {
          row.invoicingItem = "代理运费"
        } else if (chargeName.includes("报关")) {
          row.invoicingItem = "代理报关服务费"
        } else if (chargeName.includes("拖车")) {
          row.invoicingItem = "代理拖车费"
        } else if (chargeName.includes("港杂") || chargeName.includes("码头")) {
          row.invoicingItem = "代理港杂费"
        } else if (chargeName.includes("文件")) {
          row.invoicingItem = "代理文件费"
        } else if (chargeName.includes("操作")) {
          row.invoicingItem = "代理操作费"
        } else {
          // 默认设置为代理服务费
          row.invoicingItem = "代理服务费"
        }
      })

      this.$message.success(`已为 ${this.selectedRows.length} 项设置默认开票项目`)

      // 重新计算发票金额
      this.calculateInvoiceAmount()
    },

    // 批量清空开票项目
    batchClearInvoicingItem() {
      this.$confirm("确定要清空选中项的开票项目吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        customClass: "modal-confirm"
      }).then(() => {
        this.selectedRows.forEach(row => {
          row.invoicingItem = null
        })

        this.$message.success(`已清空 ${this.selectedRows.length} 项的开票项目`)

        // 重新计算发票金额
        this.calculateInvoiceAmount()
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 开票项目数据标准化函数
    invoicingItemNormalizer(node) {
      const normalized = {
        id: node.invoicingItemName, // 使用invoicingItemName作为id
        label: node.invoicingItemName,
        invoicingItemName: node.invoicingItemName
      }

      if (node.children && node.children.length > 0) {
        normalized.children = node.children.map(child => this.invoicingItemNormalizer(child))
      }

      return normalized
    },

    // 计算发票金额
    calculateInvoiceAmount() {
      // 检查必要的字段是否存在
      if (!this.formData.rsChargeList || !Array.isArray(this.formData.rsChargeList)) {
        this.formData.invoiceNetAmount = 0
        return
      }

      // 计算费用明细的总金额和税额
      // 税金
      let vatAmount = this.formData.rsChargeList.reduce((acc, charge) => {
        // 确保subtotal字段存在且为数字
        const dutyRate = parseFloat(charge.dutyRate / 100) || 0
        const vatAmount = dutyRate * (parseFloat(charge.dnAmount * charge.dnUnitRate) || 0)
        return acc + vatAmount
      }, 0)
      // 含税金
      let netAmount = this.formData.rsChargeList.reduce((acc, charge) => {
        // 确保subtotal字段存在且为数字
        const subtotal = parseFloat(charge.subtotal) || 0
        return acc + subtotal
      }, 0)
      // 不含税金
      let dnAmount = this.formData.rsChargeList.reduce((acc, charge) => {
        // 确保subtotal字段存在且为数字
        const subtotal = parseFloat(charge.dnAmount * charge.dnUnitRate) || 0
        return acc + subtotal
      }, 0)

      // 保存应收/应付
      if (this.formData.saleBuy === "sale") {
        this.formData.dnSum = netAmount
        this.formData.vatAmount = vatAmount
      } else {
        this.formData.cnSum = netAmount
        this.formData.vatAmount = vatAmount
      }

      // 根据发票币种和汇率进行相应的计算
      if (this.formData.invoiceCurrencyCode && this.formData.invoiceExchangeRate) {
        const exchangeRate = parseFloat(this.formData.invoiceExchangeRate)
        if (exchangeRate > 0) {
          // 如果汇率大于0，则进行汇率转换
          netAmount = netAmount * exchangeRate
          dnAmount = dnAmount * exchangeRate
          vatAmount = vatAmount * exchangeRate
        }
      }

      // 如果是增值税专用发票，需要加上6%的税额
      let taxAmount = 0
      if (this.formData.invoiceType === "增值税专用发票") {
        taxAmount = netAmount * 0.06
        netAmount = netAmount + taxAmount
      }

      // 保留两位小数
      // 不含税
      this.formData.invoiceNetAmount = parseFloat(dnAmount.toFixed(2))
      // 发票金额
      this.formData.invoiceAmount = "RMB" + this.formatCurrency(dnAmount) + " + " + this.formatCurrency(vatAmount) + " = " + this.formatCurrency(netAmount)
      // 结算金额
      this.formData.settlementAmount = parseFloat(netAmount.toFixed(2))
    },

    // 获取发票金额占位符文本
    getInvoiceAmountPlaceholder() {
      if (!this.formData.rsChargeList || this.formData.rsChargeList.length === 0) {
        return "请先添加费用明细"
      }
      if (!this.formData.invoiceCurrencyCode) {
        return "请选择发票币种"
      }
      if (!this.formData.invoiceExchangeRate) {
        return "请输入发票汇率"
      }
      return "金额将自动计算"
    },

    // 格式化货币显示
    formatCurrency(amount) {
      if (!amount && amount !== 0) return "0.00"
      const num = parseFloat(amount)
      if (isNaN(num)) return "0.00"
      return num.toFixed(2)
    },

    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 自动收集费用列表中的rct号，以逗号分隔填入相关订单号字段
          this.collectRctNumbers()
          this.$emit("submit", this.formData)
        }
      })
    },
    // 取消按钮
    handleCancel() {
      this.$emit("cancel")
      this.handleClose()
    },
    // 关闭对话框
    handleClose() {
      this.$emit("update:visible", false)
    },
    searchAvailableInvoice() {
      console.log("检索可用发票")
    },

    // 获取公司银行账户列表
    fetchCompanyBankAccounts() {
      // 检查是否有选择对方公司
      if (!this.formData.cooperatorId) {
        this.$message.warning("请先选择对方公司")
        return
      }

      // 调用API获取该公司的银行账户
      listAccount({belongToCompany: this.formData.cooperatorId}).then(response => {
        if (response.code === 200) {
          this.companyBankList = response.rows || []
          // 如果没有账户，显示提示
          if (this.companyBankList.length === 0) {
            this.$message.info("该公司没有银行账户记录")
          }
        }
      }).catch(error => {
        console.error("获取公司银行账户失败", error)
        this.$message.error("获取公司银行账户失败")
      })
    },

    // 处理银行账户选择变化
    handleBankAccountChange(bankCode) {
      // 根据选择的bankCode找到对应的银行账户信息
      const selectedAccount = this.availableBankList.find(item => item.bankCode === bankCode)

      if (selectedAccount) {
        // 自动填充银行全称和银行账号
        this.formData.cooperatorBankFullname = selectedAccount.bankBranchName || ""
        this.formData.cooperatorBankAccount = selectedAccount.bankAccount || ""
      }
    },
    // 获取公司信息
    fetchCompanyInfo(companyId) {
      if (!companyId) {
        return
      }
      getCompany(companyId).then(response => {
        if (response.code === 200) {
          const companyInfo = response.data
          // 更新表单中与对方公司相关的字段
          this.formData.cooperatorCompanyTitle = companyInfo.companyLocalName || ""
          this.formData.cooperatorVatSerialNo = companyInfo.taxNo || ""
          this.formData.cooperatorShortName = companyInfo.companyShortName || ""
        }
      }).catch(error => {
        console.error("获取公司信息失败", error)
      })
    },

    // 自动填充我司发票抬头和税号
    autoFillCompanyInfo(companyCode) {
      // 根据所属公司代码(GZRS/HKRS/SZRS/GZCF)自动填充我司发票抬头和税号
      const companyInfoMap = {
        "GZRS": {
          title: "广州瑞旗国际货运代理有限公司",
          taxNo: "91440101MA59UQXX7B"
        },
        "HKRS": {
          title: "香港瑞旗国际货运代理有限公司",
          taxNo: "HK12345678"
        },
        "SZRS": {
          title: "深圳市瑞旗国际货运代理有限公司",
          taxNo: "91440300MA5G9UB57Q"
        },
        "GZCF": {
          title: "广州正泽国际货运代理有限公司",
          taxNo: "91440101MA9XRGLH0F"
        }
      }

      // 获取对应公司的信息
      const companyInfo = companyInfoMap[companyCode]

      // 如果找到对应的公司信息，则填充表单
      if (companyInfo) {
        this.formData.richCompanyTitle = companyInfo.title
        this.formData.richVatSerialNo = companyInfo.taxNo
      }
    },

    // 判断表单项是否禁用
    isDisabled() {
      // 根据以下条件判断表单是否应该禁用：
      // 1. 如果发票状态为已开票
      if (this.type === "debitNote" && this.formData.invoiceStatus === "applied") {
        return true
      }

      // 2. 如果报税已锁定
      if (this.formData.taxLocked === "1") {
        return true
      }

      // 3. 如果已支付
      // if (this.formData.actualPayDate) {
      //   return true
      // }

      // 如果发票已审核，则不可编辑
      if (this.formData.auditStatus === "审核通过") {
        return true
      }

      // 如果没有禁用条件，则表单可编辑
      return false
    },
    handleRichBankCodeChange(row) {
      this.formData.richBankFullname = row.bankBranchName
      this.formData.richBankAccount = row.bankAccount
    },
    // 获取发票状态类型
    getInvoiceStatusType(status) {
      const statusMap = {
        "unissued": "info",
        "issued": "success",
        "applied": "warning",
        "canceled": "danger"
      }
      return statusMap[status] || "info"
    },
    // 获取发票状态文本
    getInvoiceStatusText(status) {
      const statusMap = {
        "unissued": "未开票",
        "issued": "已开票",
        "applied": "已申请",
        "confirmed": "已审核",
        "canceled": "已作废"
      }
      return statusMap[status] || "未知"
    },
    // 申请开票
    async handleApplyInvoice() {
      try {
        // 获取当前用户信息
        const currentUser = this.$store.state.user
        const currentTime = new Date().toISOString()

        // 更新发票状态为已申请
        const updateData = {
          ...this.formData,
          invoiceStatus: "applied",
          applyStuffId: currentUser.sid, // 开票人ID
          appliedTime: currentTime // 开票时间
        }

        // 调用API更新发票状态
        const response = await updateVatinvoice(updateData)

        if (response.code === 200) {
          // 更新本地数据
          this.formData.invoiceStatus = "applied"
          this.formData.applyStuffId = currentUser.sid
          this.formData.appliedTime = currentTime

          this.$message.success("申请开票成功")

          // 通知父组件数据变化
          this.$emit("applyInvoice", this.formData)
        } else {
          this.$message.error(response.msg || "申请开票失败")
        }
      } catch (error) {
        console.error("申请开票失败:", error)
        this.$message.error("申请开票失败，请重试")
      }
    },
    // 信息审核
    async handleAuditInvoice() {
      // 获取当前用户信息
      const currentUser = this.$store.state.user
      const currentTime = new Date().toISOString()

      // 更新审核信息
      this.formData = {
        ...this.formData,
        invoiceStatus: "confirmed",
        auditStuffId: currentUser.sid, // 审核人ID
        auditTime: currentTime, // 审核时间
        auditStatus: "审核通过" // 审核状态
      }

      // 通知父组件数据变化
      this.$emit("auditInvoice", this.formData)
    },
    // 发送开票
    async handleSendInvoice() {
      try {
        // 检查审核状态，只有审核通过的发票才能开票
        if (this.formData.auditStatus !== "审核通过") {
          this.$message.error("只有审核通过的发票才能进行开票操作")
          return
        }

        // 获取当前用户信息
        const currentUser = this.$store.state.user
        const currentTime = new Date().toISOString()

        // 更新开票信息
        const updateData = {
          ...this.formData,
          invoiceStatus: "issued", // 发票状态为已开票
          issuedStuffId: currentUser.sid, // 开票人ID
          issuedTime: currentTime // 开票时间
        }

        // 调用API更新发票开票信息
        const response = await updateVatinvoice(updateData)

        if (response.code === 200) {
          // 更新本地数据
          this.formData.invoiceStatus = "issued"
          this.formData.issuedStuffId = currentUser.sid
          this.formData.issuedTime = currentTime

          this.$message.success("发送开票成功")

          // 通知父组件数据变化
          this.$emit("sendInvoice", this.formData)
        } else {
          this.$message.error(response.msg || "发送开票失败")
        }
      } catch (error) {
        console.error("发送开票失败:", error)
        this.$message.error("发送开票失败，请重试")
      }
    }
    ,
    // 判断是否可以申请开票
    canApplyInvoice() {
      // 检查是否有发票ID
      if (!this.formData.invoiceId) {
        return false
      }

      // 检查发票状态
      if (this.formData.invoiceStatus === "issued") {
        return false // 已开票不能再次申请
      }

      if (this.formData.invoiceStatus === "applied") {
        return false // 已申请不能重复申请
      }

      // 检查是否已报税锁定
      if (this.formData.taxLocked === "1") {
        return false
      }

      // 检查是否已支付（如果需要的话）
      // if (this.formData.actualPayDate) {
      //   return false
      // }

      return true
    }
    ,
    // 获取申请信息
    getApplyInfo() {
      if (this.formData.invoiceStatus === "applied") {
        // 如果已申请开票，显示申请人+时间
        const currentUser = this.$store.state.user
        let applyTime = ""

        if (this.formData.issuedTime) {
          try {
            applyTime = new Date(this.formData.issuedTime).toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit"
            })
          } catch (e) {
            applyTime = "时间格式错误"
          }
        }

        const userName = this.getName(currentUser.sid) || "未知用户"
        return `${userName}${applyTime}`
      }
      if (this.formData.invoiceStatus === "issued") {
        return "已开票"
      }
      if (this.formData.taxLocked === "1") {
        return "已报税锁定"
      }
      if (this.formData.actualPayDate) {
        return "已支付"
      }
      return "未申请"
    }
    ,
    getName(id) {
      if (id) {
        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]
        if (staff) {
          return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName
        } else {
          return ""
        }
      } else {
        return ""
      }
    }
    ,
    loadStaff() {
      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {
        store.dispatch("getAllRsStaffList").then(() => {
          this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === "业务部")
        })
      } else {
        this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === "业务部")
      }
    }
    ,

    // 自动收集费用列表中的rct号，以逗号分隔填入相关订单号字段
    collectRctNumbers() {
      if (!this.formData.rsChargeList || !Array.isArray(this.formData.rsChargeList)) {
        return
      }

      // 收集所有不重复的rct号
      const rctNumbers = new Set()
      this.formData.rsChargeList.forEach(charge => {
        if (charge.sqdRctNo && charge.sqdRctNo.trim()) {
          rctNumbers.add(charge.sqdRctNo.trim())
        }
      })

      // 将rct号转换为数组并排序，然后以逗号分隔
      const rctArray = Array.from(rctNumbers).sort()
      this.formData.relatedOrderNo = rctArray.join(",")
    }
  }
}
</script>

<style scoped>
.yellow-bg {
  background-color: #fffbe6;
}

.disable-form {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.uploaded-file-preview {
  margin-top: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.preview-title {
  font-weight: bold;
  color: #495057;
  margin-bottom: 8px;
  font-size: 14px;
}

.file-links {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.file-links .el-link {
  font-size: 12px;
}

/* treeselect组件样式 */
:deep(.vue-treeselect__menu) {
  z-index: 9999 !important;
  position: fixed !important;
}

:deep(.vue-treeselect__menu-container) {
  z-index: 9999 !important;
}

:deep(.vue-treeselect__dropdown) {
  z-index: 9999 !important;
}

/* 确保下拉框在表格之上 */
:deep(.vue-treeselect__menu-arrow) {
  z-index: 9999 !important;
}

:deep(.vue-treeselect__list) {
  z-index: 9999 !important;
}

/* 金额公式样式 */
.amount-formula {
  margin-top: 5px;
  padding: 5px 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.amount-formula small {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.text-muted {
  color: #909399 !important;
}
</style>
