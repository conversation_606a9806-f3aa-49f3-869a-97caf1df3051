{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\index.vue", "mtime": 1756452458551}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vatInvoice", "require", "_store", "_interopRequireDefault", "_VatinvoiceDialog", "_debitnote", "_rich", "_moment2", "_currency", "name", "computed", "moment", "components", "VatinvoiceDialog", "data", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "vatinvoiceList", "title", "open", "queryParams", "pageNum", "pageSize", "invoiceCodeNo", "invoiceOfficalNo", "saleBuy", "invoiceBelongsTo", "richBankCode", "richCompanyTitle", "richVatSerialNo", "rich<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "richBankFullname", "cooperatorId", "cooperatorShortName", "cooperatorBankCode", "cooperatorFullname", "cooperatorVatSerialNo", "cooperator<PERSON><PERSON>k<PERSON><PERSON>unt", "cooperatorBankFullname", "rctNoSummary", "cooperatorReferNo", "officalChargeNameSummary", "chargeCurrencyCode", "dnSum", "dnRecieved", "dnBalance", "cnSum", "cnPaid", "cnBalance", "chargeClearStatus", "expectedPayDate", "approvedPayDate", "actualPayDate", "sqdBankStatementList", "invoiceCurrencyCode", "invoiceExchangeRate", "invoiceNetAmount", "vatAmount", "invoiceVatAmount", "saleNetSum", "saleTax", "saleTaxTotal", "buyNetSum", "buyTax", "buyTaxTotal", "taxClass", "invoiceType", "belongsToMonth", "invoiceStatus", "invoiceRemark", "applyStuffId", "appliedTime", "issuedStuffId", "issuedTime", "taxStuffId", "taxDeclareTime", "form", "rules", "required", "message", "trigger", "invoiceItemList", "companyList", "bankAccountList", "watch", "n", "created", "getList", "loadStaff", "methods", "selectableRow", "row", "auditStuffId", "handleInvoiceAuditCancel", "formData", "_this", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "response", "updateData", "updateResponse", "wrap", "_callee$", "_context", "prev", "next", "updateVatinvoice", "sent", "invoiceId", "updateDebitNoteByInvoiceId", "code", "$message", "success", "error", "stop", "handleInvoiceApplyCancel", "_this2", "_callee2", "_callee2$", "_context2", "currency", "parseTime", "handleRowClick", "handleUpdate", "_this3", "listVatinvoice", "then", "rows", "cancel", "reset", "createTime", "updateTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this4", "text", "status", "$modal", "confirm", "changeStatus", "msgSuccess", "catch", "isSelectable", "mergeInvoice", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "_this5", "getVatinvoice", "extCompanyList", "basAccountList", "submitForm", "handleDialogSubmit", "_this6", "addVatinvoice", "handleAuditInvoice", "_this7", "_callee3", "response2", "_callee3$", "_context3", "rctId", "generateInvoiceCodeNo", "rsChargeList", "charge", "_objectSpread2", "sqdInvoiceCodeNo", "_callee4", "_callee4$", "_context4", "generateInvoiceCode", "abrupt", "msg", "t0", "console", "handleSendInvoice", "_this8", "handleDelete", "_this9", "invoiceIds", "delVatinvoice", "handleExportTheInvoicingInformation", "_this10", "msgError", "generateInvoiceExcel", "fileName", "concat", "Date", "getTime", "blob", "Blob", "type", "link", "document", "createElement", "url", "window", "URL", "createObjectURL", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleMergeInvoices", "_this11", "selectedInvoices", "filter", "includes", "firstInvoice", "canMerge", "every", "invoice", "mergeInvoices", "handleExport", "getName", "id", "staff", "$store", "state", "allRsStaffList", "rsStaff", "staffId", "staffShortName", "_this12", "redisList", "store", "dispatch", "staffList", "dept", "deptLocalName", "exports", "_default"], "sources": ["src/views/system/vatinvoice/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 label-width=\"68px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"发票流水号\" prop=\"invoiceCodeNo\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceCodeNo\"\r\n              clearable\r\n              placeholder=\"发票流水号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票号码\" prop=\"invoiceOfficalNo\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceOfficalNo\"\r\n              clearable\r\n              placeholder=\"发票号码\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属公司\" prop=\"invoiceBelongsTo\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceBelongsTo\"\r\n              clearable\r\n              placeholder=\"所属公司\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司账户简称\" prop=\"richBankCode\">\r\n            <el-input\r\n              v-model=\"queryParams.richBankCode\"\r\n              clearable\r\n              placeholder=\"我司账户简称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司发票抬头\" prop=\"richCompanyTitle\">\r\n            <el-input\r\n              v-model=\"queryParams.richCompanyTitle\"\r\n              clearable\r\n              placeholder=\"我司发票抬头\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司纳税人识别号\" prop=\"richVatSerialNo\">\r\n            <el-input\r\n              v-model=\"queryParams.richVatSerialNo\"\r\n              clearable\r\n              placeholder=\"我司纳税人识别号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司账号\" prop=\"richBankAccount\">\r\n            <el-input\r\n              v-model=\"queryParams.richBankAccount\"\r\n              clearable\r\n              placeholder=\"我司账号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司银行全称\" prop=\"richBankFullname\">\r\n            <el-input\r\n              v-model=\"queryParams.richBankFullname\"\r\n              clearable\r\n              placeholder=\"我司银行全称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方公司ID\" prop=\"cooperatorId\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorId\"\r\n              clearable\r\n              placeholder=\"对方公司ID\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方公司简称\" prop=\"cooperatorShortName\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorShortName\"\r\n              clearable\r\n              placeholder=\"对方公司简称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方账户简称\" prop=\"cooperatorBankCode\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorBankCode\"\r\n              clearable\r\n              placeholder=\"对方账户简称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方发票抬头\" prop=\"cooperatorCompanyTitle\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorCompanyTitle\"\r\n              clearable\r\n              placeholder=\"对方发票抬头\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方纳税人识别号\" prop=\"cooperatorVatSerialNo\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorVatSerialNo\"\r\n              clearable\r\n              placeholder=\"对方纳税人识别号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方账号\" prop=\"cooperatorBankAccount\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorBankAccount\"\r\n              clearable\r\n              placeholder=\"对方账号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方银行全称\" prop=\"cooperatorBankFullname\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorBankFullname\"\r\n              clearable\r\n              placeholder=\"对方银行全称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"结算币种\" prop=\"chargeCurrencyCode\">\r\n            <el-input\r\n              v-model=\"queryParams.chargeCurrencyCode\"\r\n              clearable\r\n              placeholder=\"结算币种\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"账单应收总额\" prop=\"dnSum\">\r\n            <el-input\r\n              v-model=\"queryParams.dnSum\"\r\n              clearable\r\n              placeholder=\"账单应收总额\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行已收\" prop=\"dnRecieved\">\r\n            <el-input\r\n              v-model=\"queryParams.dnRecieved\"\r\n              clearable\r\n              placeholder=\"银行已收\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行未收\" prop=\"dnBalance\">\r\n            <el-input\r\n              v-model=\"queryParams.dnBalance\"\r\n              clearable\r\n              placeholder=\"银行未收\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"账单应付总额\" prop=\"cnSum\">\r\n            <el-input\r\n              v-model=\"queryParams.cnSum\"\r\n              clearable\r\n              placeholder=\"账单应付总额\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行已付\" prop=\"cnPaid\">\r\n            <el-input\r\n              v-model=\"queryParams.cnPaid\"\r\n              clearable\r\n              placeholder=\"银行已付\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行未付\" prop=\"cnBalance\">\r\n            <el-input\r\n              v-model=\"queryParams.cnBalance\"\r\n              clearable\r\n              placeholder=\"银行未付\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"期望支付日期\" prop=\"expectedPayDate\">\r\n            <el-date-picker v-model=\"queryParams.expectedPayDate\"\r\n                            clearable\r\n                            placeholder=\"期望支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"批复支付日期\" prop=\"approvedPayDate\">\r\n            <el-date-picker v-model=\"queryParams.approvedPayDate\"\r\n                            clearable\r\n                            placeholder=\"批复支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"实际支付日期\" prop=\"actualPayDate\">\r\n            <el-date-picker v-model=\"queryParams.actualPayDate\"\r\n                            clearable\r\n                            placeholder=\"实际支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"发票币种\" prop=\"invoiceCurrencyCode\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceCurrencyCode\"\r\n              clearable\r\n              placeholder=\"发票币种\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票汇率\" prop=\"invoiceExchangeRate\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceExchangeRate\"\r\n              clearable\r\n              placeholder=\"发票汇率\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"不含税金额\" prop=\"invoiceNetAmount\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceNetAmount\"\r\n              clearable\r\n              placeholder=\"不含税金额\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"税金\" prop=\"vatAmount\">\r\n            <el-input\r\n              v-model=\"queryParams.vatAmount\"\r\n              clearable\r\n              placeholder=\"税金\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"价税合计\" prop=\"invoiceVatAmount\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceVatAmount\"\r\n              clearable\r\n              placeholder=\"价税合计\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项不含税\" prop=\"saleNetSum\">\r\n            <el-input\r\n              v-model=\"queryParams.saleNetSum\"\r\n              clearable\r\n              placeholder=\"销项不含税\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项税金\" prop=\"saleTax\">\r\n            <el-input\r\n              v-model=\"queryParams.saleTax\"\r\n              clearable\r\n              placeholder=\"销项税金\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项含税合计\" prop=\"saleTaxTotal\">\r\n            <el-input\r\n              v-model=\"queryParams.saleTaxTotal\"\r\n              clearable\r\n              placeholder=\"销项含税合计\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项不含税\" prop=\"buyNetSum\">\r\n            <el-input\r\n              v-model=\"queryParams.buyNetSum\"\r\n              clearable\r\n              placeholder=\"进项不含税\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项税金\" prop=\"buyTax\">\r\n            <el-input\r\n              v-model=\"queryParams.buyTax\"\r\n              clearable\r\n              placeholder=\"进项税金\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项含税合计\" prop=\"buyTaxTotal\">\r\n            <el-input\r\n              v-model=\"queryParams.buyTaxTotal\"\r\n              clearable\r\n              placeholder=\"进项含税合计\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票性质\" prop=\"taxClass\">\r\n            <el-input\r\n              v-model=\"queryParams.taxClass\"\r\n              clearable\r\n              placeholder=\"发票性质\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"报税所属月份\" prop=\"belongsToMonth\">\r\n            <el-input\r\n              v-model=\"queryParams.belongsToMonth\"\r\n              clearable\r\n              placeholder=\"报税所属月份\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"申请人ID\" prop=\"applyStuffId\">\r\n            <el-input\r\n              v-model=\"queryParams.applyStuffId\"\r\n              clearable\r\n              placeholder=\"申请人ID\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"申请时间\" prop=\"appliedTime\">\r\n            <el-date-picker v-model=\"queryParams.appliedTime\"\r\n                            clearable\r\n                            placeholder=\"申请时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"开票人ID\" prop=\"issuedStuffId\">\r\n            <el-input\r\n              v-model=\"queryParams.issuedStuffId\"\r\n              clearable\r\n              placeholder=\"开票人ID\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"开票时间\" prop=\"issuedTime\">\r\n            <el-date-picker v-model=\"queryParams.issuedTime\"\r\n                            clearable\r\n                            placeholder=\"开票时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"报税人ID\" prop=\"taxStuffId\">\r\n            <el-input\r\n              v-model=\"queryParams.taxStuffId\"\r\n              clearable\r\n              placeholder=\"报税人ID\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"报税时间\" prop=\"taxDeclareTime\">\r\n            <el-date-picker v-model=\"queryParams.taxDeclareTime\"\r\n                            clearable\r\n                            placeholder=\"报税时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:edit']\"\r\n              :disabled=\"single\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleUpdate\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <!-- <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col> -->\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExportTheInvoicingInformation\"\r\n            >导出开票资料\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:add']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-connection\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"info\"\r\n              @click=\"handleMergeInvoices\"\r\n            >合并发票\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"vatinvoiceList\" @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"handleRowClick\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" :selectable=\"selectableRow\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"发票流水号\" prop=\"invoiceCodeNo\" width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"发票号码\" prop=\"invoiceOfficalNo\" width=\"120\">\r\n            <template #default=\"scope\">\r\n              {{ scope.row.invoiceStatus }} {{ scope.row.invoiceOfficalNo }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"所属公司\" prop=\"invoiceBelongsTo\" width=\"50\"/>\r\n          <el-table-column align=\"center\" label=\"进销项\" prop=\"saleBuy\" width=\"50\">\r\n            <template #default=\"scope\">\r\n              {{ scope.row.saleBuy == \"sale\" ? \"+\" : \"-\" }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"发票性质\" prop=\"taxClass\" show-overflow-tooltip width=\"60\"/>\r\n          <el-table-column align=\"center\" label=\"发票类型\" prop=\"invoiceType\" show-overflow-tooltip width=\"60\"/>\r\n          <el-table-column align=\"center\" label=\"对方发票抬头\" prop=\"cooperatorCompanyTitle\" show-overflow-tooltip\r\n                           width=\"80\"\r\n          />\r\n          <el-table-column align=\"center\" label=\"结算金额\" prop=\"settlementAmount\" show-overflow-tooltip width=\"80\"/>\r\n          <el-table-column align=\"center\" label=\"发票币种\" prop=\"invoiceCurrencyCode\" width=\"60\"/>\r\n          <el-table-column align=\"center\" label=\"发票汇率\" prop=\"invoiceExchangeRate\" width=\"60\"/>\r\n          <el-table-column align=\"right\" label=\"发票金额\" prop=\"invoiceAmount\" width=\"180\">\r\n            <template #default=\"scope\">\r\n              {{\r\n                currency(scope.row.invoiceNetAmount).format({symbol: \"\", decimal: \".\", precision: 2})\r\n              }}+{{\r\n                currency(scope.row.vatAmount).format({symbol: \"\", decimal: \".\", precision: 2})\r\n              }}={{ currency(scope.row.invoiceVatAmount).format({symbol: \"\", decimal: \".\", precision: 2}) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"开票要求\" prop=\"opRemark\" show-overflow-tooltip width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"期望支付\" prop=\"expectedPayDate\" show-overflow-tooltip width=\"60\">\r\n            <template #default=\"scope\">\r\n              <span>{{ scope.row.expectedPayDate ? moment(scope.row.expectedPayDate).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"批复支付\" prop=\"approvedPayDate\" show-overflow-tooltip width=\"60\">\r\n            <template #default=\"scope\">\r\n              <span>{{ scope.row.approvedPayDate ? moment(scope.row.approvedPayDate).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"实际支付\" prop=\"actualPayDate\" show-overflow-tooltip width=\"60\">\r\n            <template #default=\"scope\">\r\n              <span>{{ scope.row.actualPayDate ? moment(scope.row.actualPayDate).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"计税月份\" prop=\"belongsToMonth\" show-overflow-tooltip width=\"80\"/>\r\n          <el-table-column align=\"center\" label=\"申请人\" prop=\"applyStuffId\" show-overflow-tooltip>\r\n            <template #default=\"scope\">\r\n              <span>{{\r\n                  getName(scope.row.applyStuffId)\r\n                }}-{{ scope.row.appliedTime ? moment(scope.row.appliedTime).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"审核人\" prop=\"auditStuffId\" show-overflow-tooltip>\r\n            <template #default=\"scope\">\r\n              <span>{{\r\n                  getName(scope.row.auditStuffId)\r\n                }}-{{ scope.row.auditTime ? moment(scope.row.auditTime).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"开票人\" prop=\"issuedStuffId\" show-overflow-tooltip>\r\n            <template #default=\"scope\">\r\n              <span>{{\r\n                  getName(scope.row.issuedStuffId)\r\n                }}-{{ scope.row.issuedTime ? moment(scope.row.issuedTime).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"报税人\" prop=\"taxStuffId\" show-overflow-tooltip>\r\n            <template #default=\"scope\">\r\n              <span>{{\r\n                  getName(scope.row.taxStuffId)\r\n                }}-{{ scope.row.taxDeclareTime ? moment(scope.row.taxDeclareTime).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改发票登记对话框 -->\r\n    <vatinvoice-dialog\r\n      :form=\"form\"\r\n      :invoice-items=\"invoiceItemList\"\r\n      :bank-account-list=\"bankAccountList\"\r\n      :company-list=\"companyList\"\r\n      :rules=\"rules\"\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      @cancel=\"cancel\"\r\n      @submit=\"handleDialogSubmit\"\r\n      @auditInvoice=\"handleAuditInvoice\"\r\n      @sendInvoice=\"handleSendInvoice\"\r\n      @invoiceApplyCancel=\"handleInvoiceApplyCancel\"\r\n      @invoiceAuditCancel=\"handleInvoiceAuditCancel\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addVatinvoice,\r\n  changeStatus,\r\n  delVatinvoice,\r\n  getVatinvoice,\r\n  listVatinvoice,\r\n  updateVatinvoice,\r\n  generateInvoiceExcel,\r\n  mergeInvoices, generateInvoiceCode\r\n} from \"@/api/system/vatInvoice\"\r\nimport store from \"@/store\"\r\nimport VatinvoiceDialog from \"./components/VatinvoiceDialog\"\r\nimport {updateDebitNoteByInvoiceId} from \"@/api/system/debitnote\"\r\nimport {parseTime} from \"../../../utils/rich\"\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\n\r\nexport default {\r\n  name: \"Vatinvoice\",\r\n  computed: {\r\n    moment() {\r\n      return moment\r\n    }\r\n  },\r\n  components: {\r\n    VatinvoiceDialog\r\n  },\r\n  data() {\r\n    return {\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 发票登记表格数据\r\n      vatinvoiceList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        invoiceCodeNo: null,\r\n        invoiceOfficalNo: null,\r\n        saleBuy: null,\r\n        invoiceBelongsTo: null,\r\n        richBankCode: null,\r\n        richCompanyTitle: null,\r\n        richVatSerialNo: null,\r\n        richBankAccount: null,\r\n        richBankFullname: null,\r\n        cooperatorId: null,\r\n        cooperatorShortName: null,\r\n        cooperatorBankCode: null,\r\n        cooperatorFullname: null,\r\n        cooperatorVatSerialNo: null,\r\n        cooperatorBankAccount: null,\r\n        cooperatorBankFullname: null,\r\n        rctNoSummary: null,\r\n        cooperatorReferNo: null,\r\n        officalChargeNameSummary: null,\r\n        chargeCurrencyCode: null,\r\n        dnSum: null,\r\n        dnRecieved: null,\r\n        dnBalance: null,\r\n        cnSum: null,\r\n        cnPaid: null,\r\n        cnBalance: null,\r\n        chargeClearStatus: null,\r\n        expectedPayDate: null,\r\n        approvedPayDate: null,\r\n        actualPayDate: null,\r\n        sqdBankStatementList: null,\r\n        invoiceCurrencyCode: null,\r\n        invoiceExchangeRate: null,\r\n        invoiceNetAmount: null,\r\n        vatAmount: null,\r\n        invoiceVatAmount: null,\r\n        saleNetSum: null,\r\n        saleTax: null,\r\n        saleTaxTotal: null,\r\n        buyNetSum: null,\r\n        buyTax: null,\r\n        buyTaxTotal: null,\r\n        taxClass: null,\r\n        invoiceType: null,\r\n        belongsToMonth: null,\r\n        invoiceStatus: null,\r\n        invoiceRemark: null,\r\n        applyStuffId: null,\r\n        appliedTime: null,\r\n        issuedStuffId: null,\r\n        issuedTime: null,\r\n        taxStuffId: null,\r\n        taxDeclareTime: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        invoiceCodeNo: [\r\n          {\r\n            required: true,\r\n            message: \"发票流水号不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        saleBuy: [\r\n          {\r\n            required: true,\r\n            message: \"进销标志：sale=销项，buy=进项不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      },\r\n      invoiceItemList: [],\r\n      companyList: [],\r\n      bankAccountList: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadStaff()\r\n  },\r\n  methods: {\r\n    selectableRow(row) {\r\n      return row.auditStuffId !== null\r\n    },\r\n    async handleInvoiceAuditCancel(formData) {\r\n      // 修改发票\r\n      const response = await updateVatinvoice(formData)\r\n      // 更新分账单\r\n      const updateData = {\r\n        invoiceId: formData.invoiceId,\r\n        invoiceStatus: \"unissued\"\r\n      }\r\n      const updateResponse = await updateDebitNoteByInvoiceId(updateData)\r\n      if (updateResponse.code === 200 && response.code === 200) {\r\n        this.$message.success(\"取消审核成功\")\r\n        this.getList()\r\n        this.open = false\r\n      } else {\r\n        this.$message.error(\"取消审核失败\")\r\n      }\r\n    },\r\n    async handleInvoiceApplyCancel(formData) {\r\n      // 修改发票\r\n      const response = await updateVatinvoice(formData)\r\n      // 更新分账单\r\n      const updateData = {\r\n        invoiceId: formData.invoiceId,\r\n        invoiceStatus: \"unissued\"\r\n      }\r\n      const updateResponse = await updateDebitNoteByInvoiceId(updateData)\r\n      if (updateResponse.code === 200 && response.code === 200) {\r\n        this.$message.success(\"取消申请成功\")\r\n        this.getList()\r\n        this.open = false\r\n      } else {\r\n        this.$message.error(\"取消申请失败\")\r\n      }\r\n    },\r\n    currency,\r\n    parseTime,\r\n    handleRowClick(row) {\r\n      this.handleUpdate(row)\r\n    },\r\n    /** 查询发票登记列表 */\r\n    getList() {\r\n      this.loading = true\r\n      // 不显示未申请开票的发票\r\n      listVatinvoice(this.queryParams).then(response => {\r\n        this.vatinvoiceList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        invoiceId: null,\r\n        invoiceCodeNo: null,\r\n        invoiceOfficalNo: null,\r\n        saleBuy: null,\r\n        invoiceBelongsTo: null,\r\n        richBankCode: null,\r\n        richCompanyTitle: null,\r\n        richVatSerialNo: null,\r\n        richBankAccount: null,\r\n        richBankFullname: null,\r\n        cooperatorId: null,\r\n        cooperatorShortName: null,\r\n        cooperatorBankCode: null,\r\n        cooperatorFullname: null,\r\n        cooperatorVatSerialNo: null,\r\n        cooperatorBankAccount: null,\r\n        cooperatorBankFullname: null,\r\n        rctNoSummary: null,\r\n        cooperatorReferNo: null,\r\n        officalChargeNameSummary: null,\r\n        chargeCurrencyCode: null,\r\n        dnSum: null,\r\n        dnRecieved: null,\r\n        dnBalance: null,\r\n        cnSum: null,\r\n        cnPaid: null,\r\n        cnBalance: null,\r\n        chargeClearStatus: \"0\",\r\n        expectedPayDate: null,\r\n        approvedPayDate: null,\r\n        actualPayDate: null,\r\n        sqdBankStatementList: null,\r\n        invoiceCurrencyCode: null,\r\n        invoiceExchangeRate: null,\r\n        invoiceNetAmount: null,\r\n        vatAmount: null,\r\n        invoiceVatAmount: null,\r\n        saleNetSum: null,\r\n        saleTax: null,\r\n        saleTaxTotal: null,\r\n        buyNetSum: null,\r\n        buyTax: null,\r\n        buyTaxTotal: null,\r\n        taxClass: null,\r\n        invoiceType: null,\r\n        belongsToMonth: null,\r\n        invoiceStatus: \"0\",\r\n        invoiceRemark: null,\r\n        applyStuffId: null,\r\n        appliedTime: null,\r\n        issuedStuffId: null,\r\n        issuedTime: null,\r\n        taxStuffId: null,\r\n        taxDeclareTime: null,\r\n        createTime: null,\r\n        updateTime: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.invoiceId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 判断行是否可选\r\n    isSelectable(row) {\r\n      // 发票状态为merged的行不可勾选\r\n      return row.invoiceStatus !== \"merged\" && row.mergeInvoice === \"1\"\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.invoiceId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加发票登记\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const invoiceId = row.invoiceId || this.ids\r\n      getVatinvoice(invoiceId).then(response => {\r\n        this.form = response.data\r\n        this.companyList = response.extCompanyList\r\n        this.bankAccountList = response.basAccountList\r\n        this.open = true\r\n        this.title = \"修改发票登记\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 已移至handleDialogSubmit方法\r\n    },\r\n\r\n    /** 处理对话框提交 */\r\n    handleDialogSubmit(formData) {\r\n      if (formData.invoiceId != null) {\r\n        updateVatinvoice(formData).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n          this.open = false\r\n          this.getList()\r\n        })\r\n      } else {\r\n        addVatinvoice(formData).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\")\r\n          this.open = false\r\n          this.getList()\r\n        })\r\n      }\r\n    },\r\n    /** 处理审核发票事件 */\r\n    async handleAuditInvoice(row) {\r\n      let invoiceCodeNo\r\n      if (row.rctId && row.cooperatorId) {\r\n        invoiceCodeNo = await this.generateInvoiceCodeNo(row.rctId, row.cooperatorId)\r\n      }\r\n      row.invoiceCodeNo = invoiceCodeNo\r\n      row.rsChargeList = row.rsChargeList.map(charge => {\r\n        return {\r\n          ...charge,\r\n          sqdInvoiceCodeNo: invoiceCodeNo\r\n        }\r\n      })\r\n      // 调用API更新发票审核信息\r\n      const response = await updateVatinvoice(row)\r\n\r\n      // 更新账单发票信息->已审核\r\n      const response2 = await updateDebitNoteByInvoiceId({\r\n        invoiceId: row.invoiceId,\r\n        invoiceCodeNo: invoiceCodeNo,\r\n        invoiceStatus: \"confirmed\"\r\n      })\r\n\r\n      if (response.code === 200 && response2.code === 200) {\r\n        this.$message.success(\"信息审核成功\")\r\n        this.getList()\r\n      }\r\n    },\r\n    // 生成发票流水号\r\n    async generateInvoiceCodeNo(rctId, cooperatorId) {\r\n      try {\r\n        // 调用API生成发票编码\r\n        const response = await generateInvoiceCode(rctId, cooperatorId)\r\n        if (response.code === 200) {\r\n          return response.msg\r\n        }\r\n      } catch (error) {\r\n        console.error(\"生成发票编码失败:\", error)\r\n      }\r\n      // 如果API调用失败，返回默认格式\r\n      return \"\"\r\n    },\r\n    /** 处理发送开票事件 */\r\n    handleSendInvoice(formData) {\r\n      // 更新debitNote为已开票\r\n      const invoiceId = formData.invoiceId\r\n      if (invoiceId) {\r\n        updateDebitNoteByInvoiceId({invoiceId, invoiceStatus: \"issued\"})\r\n          .then(response => {\r\n            if (response.code === 200) {\r\n              this.$message.success(\"分账单发票状态更新成功\")\r\n            } else {\r\n              this.$message.error(response.msg || \"分账单发票状态更新失败\")\r\n            }\r\n          })\r\n          .catch(error => {\r\n            console.error(\"分账单发票状态更新失败:\", error)\r\n            this.$message.error(\"分账单发票状态更新失败，请重试\")\r\n          })\r\n      }\r\n\r\n      // 刷新列表\r\n      this.getList()\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const invoiceIds = row.invoiceId || this.ids\r\n      this.$modal.confirm(\"是否确认删除发票登记编号为\\\"\" + invoiceIds + \"\\\"的数据项？\").then(function () {\r\n        return delVatinvoice(invoiceIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出开票资料按钮操作 */\r\n    handleExportTheInvoicingInformation() {\r\n      // 检查是否有选中的发票\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请先选择要导出的发票\")\r\n        return\r\n      }\r\n\r\n      generateInvoiceExcel(this.ids)\r\n        .then(response => {\r\n          // 获取文件的字节数组 (ArrayBuffer)\r\n          const data = response\r\n\r\n          // 生成文件名\r\n          let fileName = `Invoices_${new Date().getTime()}.xlsx`\r\n\r\n          // 创建一个 Blob 对象来存储文件\r\n          const blob = new Blob([data], {\r\n            type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"  // Excel 文件类型\r\n          })\r\n\r\n          // 创建一个临时链接，模拟点击来下载文件\r\n          const link = document.createElement(\"a\")\r\n          const url = window.URL.createObjectURL(blob)  // 创建一个 URL 指向 Blob 对象\r\n          link.href = url\r\n          link.download = fileName  // 设置下载的文件名\r\n\r\n          // 模拟点击链接，触发下载\r\n          document.body.appendChild(link)\r\n          link.click()\r\n\r\n          // 下载完成后移除链接，并释放 URL 对象\r\n          document.body.removeChild(link)\r\n          window.URL.revokeObjectURL(url)\r\n\r\n          this.$modal.msgSuccess(\"Excel文件导出成功\")\r\n        })\r\n        .catch(error => {\r\n          console.error(\"文件下载失败:\", error)\r\n          this.$modal.msgError(\"Excel文件导出失败\")\r\n        })\r\n    },\r\n    /** 合并发票按钮操作 */\r\n    handleMergeInvoices() {\r\n\r\n      if (this.isSelectable) {\r\n        this.$modal.msgError(\"选中的发票有不能合并的项目\")\r\n        return\r\n      }\r\n\r\n      // 检查是否有选中的发票\r\n      if (this.ids.length < 2) {\r\n        this.$modal.msgError(\"请至少选择2张发票进行合并\")\r\n        return\r\n      }\r\n\r\n      // 获取选中的发票数据\r\n      const selectedInvoices = this.vatinvoiceList.filter(item => this.ids.includes(item.invoiceId))\r\n\r\n      // 检查发票状态和类型是否一致\r\n      const firstInvoice = selectedInvoices[0]\r\n      const canMerge = selectedInvoices.every(invoice => {\r\n        // 检查发票状态 - 只能合并未开票的发票\r\n        if (invoice.invoiceStatus !== \"unissued\" && invoice.invoiceStatus !== \"applied\") {\r\n          return false\r\n        }\r\n        // 检查进销项是否一致\r\n        if (invoice.saleBuy !== firstInvoice.saleBuy) {\r\n          return false\r\n        }\r\n        // 检查对方公司是否一致\r\n        if (invoice.cooperatorId !== firstInvoice.cooperatorId) {\r\n          return false\r\n        }\r\n        // 检查币种是否一致\r\n        if (invoice.invoiceCurrencyCode !== firstInvoice.invoiceCurrencyCode) {\r\n          return false\r\n        }\r\n        return true\r\n      })\r\n\r\n      if (!canMerge) {\r\n        this.$modal.msgError(\"只能合并状态为未开票或已申请、进销项一致、对方公司一致、币种一致的发票\")\r\n        return\r\n      }\r\n\r\n      // 确认合并操作\r\n      this.$modal.confirm(`确认要合并选中的 ${this.ids.length} 张发票吗？合并后将生成一张新的发票，原发票将被标记为已合并。`).then(() => {\r\n        mergeInvoices(this.ids).then(response => {\r\n          if (response.code === 200) {\r\n            this.$modal.msgSuccess(\"发票合并成功\")\r\n            this.getList() // 刷新列表\r\n          } else {\r\n            this.$modal.msgError(response.msg || \"发票合并失败\")\r\n          }\r\n        }).catch(error => {\r\n          console.error(\"发票合并失败:\", error)\r\n          this.$modal.msgError(\"发票合并失败，请重试\")\r\n        })\r\n      }).catch(() => {\r\n        // 用户取消操作\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/vatinvoice/export\", {\r\n        ...this.queryParams\r\n      }, `vatinvoice_${new Date().getTime()}.xlsx`)\r\n    },\r\n    getName(id) {\r\n      if (id) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff) {\r\n          return staff.staffShortName\r\n        } else {\r\n          return \"\"\r\n        }\r\n      } else {\r\n        return \"\"\r\n      }\r\n    },\r\n    loadStaff() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === \"业务部\")\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === \"业务部\")\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA2iBA,IAAAA,WAAA,GAAAC,OAAA;AAUA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,iBAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,UAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,QAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,SAAA,GAAAL,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAQ,IAAA;EACAC,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,OAAAA,gBAAA;IACA;EACA;EACAC,UAAA;IACAC,gBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,aAAA;QACAC,gBAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,mBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,wBAAA;QACAC,kBAAA;QACAC,KAAA;QACAC,UAAA;QACAC,SAAA;QACAC,KAAA;QACAC,MAAA;QACAC,SAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,eAAA;QACAC,aAAA;QACAC,oBAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,OAAA;QACAC,YAAA;QACAC,SAAA;QACAC,MAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,cAAA;QACAC,aAAA;QACAC,aAAA;QACAC,YAAA;QACAC,WAAA;QACAC,aAAA;QACAC,UAAA;QACAC,UAAA;QACAC,cAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAtD,aAAA,GACA;UACAuD,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAvD,OAAA,GACA;UACAqD,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,eAAA;MACAC,WAAA;MACAC,eAAA;IACA;EACA;EACAC,KAAA;IACArE,UAAA,WAAAA,WAAAsE,CAAA;MACA,IAAAA,CAAA;QACA,KAAA3E,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACA6E,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,YAAA;IACA;IACAC,wBAAA,WAAAA,yBAAAC,QAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA,EAAAC,UAAA,EAAAC,cAAA;QAAA,WAAAL,oBAAA,CAAAD,OAAA,IAAAO,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OAEA,IAAAC,4BAAA,EAAAf,QAAA;YAAA;cAAAO,QAAA,GAAAK,QAAA,CAAAI,IAAA;cACA;cACAR,UAAA;gBACAS,SAAA,EAAAjB,QAAA,CAAAiB,SAAA;gBACA3C,aAAA;cACA;cAAAsC,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAI,qCAAA,EAAAV,UAAA;YAAA;cAAAC,cAAA,GAAAG,QAAA,CAAAI,IAAA;cACA,IAAAP,cAAA,CAAAU,IAAA,YAAAZ,QAAA,CAAAY,IAAA;gBACAlB,KAAA,CAAAmB,QAAA,CAAAC,OAAA;gBACApB,KAAA,CAAAR,OAAA;gBACAQ,KAAA,CAAA5E,IAAA;cACA;gBACA4E,KAAA,CAAAmB,QAAA,CAAAE,KAAA;cACA;YAAA;YAAA;cAAA,OAAAV,QAAA,CAAAW,IAAA;UAAA;QAAA,GAAAjB,OAAA;MAAA;IACA;IACAkB,wBAAA,WAAAA,yBAAAxB,QAAA;MAAA,IAAAyB,MAAA;MAAA,WAAAvB,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAqB,SAAA;QAAA,IAAAnB,QAAA,EAAAC,UAAA,EAAAC,cAAA;QAAA,WAAAL,oBAAA,CAAAD,OAAA,IAAAO,IAAA,UAAAiB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAf,IAAA,GAAAe,SAAA,CAAAd,IAAA;YAAA;cAAAc,SAAA,CAAAd,IAAA;cAAA,OAEA,IAAAC,4BAAA,EAAAf,QAAA;YAAA;cAAAO,QAAA,GAAAqB,SAAA,CAAAZ,IAAA;cACA;cACAR,UAAA;gBACAS,SAAA,EAAAjB,QAAA,CAAAiB,SAAA;gBACA3C,aAAA;cACA;cAAAsD,SAAA,CAAAd,IAAA;cAAA,OACA,IAAAI,qCAAA,EAAAV,UAAA;YAAA;cAAAC,cAAA,GAAAmB,SAAA,CAAAZ,IAAA;cACA,IAAAP,cAAA,CAAAU,IAAA,YAAAZ,QAAA,CAAAY,IAAA;gBACAM,MAAA,CAAAL,QAAA,CAAAC,OAAA;gBACAI,MAAA,CAAAhC,OAAA;gBACAgC,MAAA,CAAApG,IAAA;cACA;gBACAoG,MAAA,CAAAL,QAAA,CAAAE,KAAA;cACA;YAAA;YAAA;cAAA,OAAAM,SAAA,CAAAL,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAG,QAAA,EAAAA,iBAAA;IACAC,SAAA,EAAAA,eAAA;IACAC,cAAA,WAAAA,eAAAlC,GAAA;MACA,KAAAmC,YAAA,CAAAnC,GAAA;IACA;IACA,eACAJ,OAAA,WAAAA,QAAA;MAAA,IAAAwC,MAAA;MACA,KAAApH,OAAA;MACA;MACA,IAAAqH,0BAAA,OAAA5G,WAAA,EAAA6G,IAAA,WAAA5B,QAAA;QACA0B,MAAA,CAAA9G,cAAA,GAAAoF,QAAA,CAAA6B,IAAA;QACAH,MAAA,CAAA/G,KAAA,GAAAqF,QAAA,CAAArF,KAAA;QACA+G,MAAA,CAAApH,OAAA;MACA;IACA;IACA;IACAwH,MAAA,WAAAA,OAAA;MACA,KAAAhH,IAAA;MACA,KAAAiH,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAxD,IAAA;QACAmC,SAAA;QACAxF,aAAA;QACAC,gBAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,mBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,wBAAA;QACAC,kBAAA;QACAC,KAAA;QACAC,UAAA;QACAC,SAAA;QACAC,KAAA;QACAC,MAAA;QACAC,SAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,eAAA;QACAC,aAAA;QACAC,oBAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,OAAA;QACAC,YAAA;QACAC,SAAA;QACAC,MAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,cAAA;QACAC,aAAA;QACAC,aAAA;QACAC,YAAA;QACAC,WAAA;QACAC,aAAA;QACAC,UAAA;QACAC,UAAA;QACAC,cAAA;QACA0D,UAAA;QACAC,UAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAApH,WAAA,CAAAC,OAAA;MACA,KAAAkE,OAAA;IACA;IACA,aACAkD,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACAE,kBAAA,WAAAA,mBAAA/C,GAAA;MAAA,IAAAgD,MAAA;MACA,IAAAC,IAAA,GAAAjD,GAAA,CAAAkD,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,WAAAH,IAAA,SAAAX,IAAA;QACA,WAAAe,wBAAA,EAAArD,GAAA,CAAAoB,SAAA,EAAApB,GAAA,CAAAkD,MAAA;MACA,GAAAZ,IAAA;QACAU,MAAA,CAAAG,MAAA,CAAAG,UAAA,CAAAL,IAAA;MACA,GAAAM,KAAA;QACAvD,GAAA,CAAAkD,MAAA,GAAAlD,GAAA,CAAAkD,MAAA;MACA;IACA;IACA;IACAM,YAAA,WAAAA,aAAAxD,GAAA;MACA;MACA,OAAAA,GAAA,CAAAvB,aAAA,iBAAAuB,GAAA,CAAAyD,YAAA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA1I,GAAA,GAAA0I,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAzC,SAAA;MAAA;MACA,KAAAlG,MAAA,GAAAyI,SAAA,CAAAG,MAAA;MACA,KAAA3I,QAAA,IAAAwI,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAtB,KAAA;MACA,KAAAjH,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA4G,YAAA,WAAAA,aAAAnC,GAAA;MAAA,IAAAgE,MAAA;MACA,KAAAvB,KAAA;MACA,IAAArB,SAAA,GAAApB,GAAA,CAAAoB,SAAA,SAAAnG,GAAA;MACA,IAAAgJ,yBAAA,EAAA7C,SAAA,EAAAkB,IAAA,WAAA5B,QAAA;QACAsD,MAAA,CAAA/E,IAAA,GAAAyB,QAAA,CAAA7F,IAAA;QACAmJ,MAAA,CAAAzE,WAAA,GAAAmB,QAAA,CAAAwD,cAAA;QACAF,MAAA,CAAAxE,eAAA,GAAAkB,QAAA,CAAAyD,cAAA;QACAH,MAAA,CAAAxI,IAAA;QACAwI,MAAA,CAAAzI,KAAA;MACA;IACA;IACA,WACA6I,UAAA,WAAAA,WAAA;MACA;IAAA,CACA;IAEA,cACAC,kBAAA,WAAAA,mBAAAlE,QAAA;MAAA,IAAAmE,MAAA;MACA,IAAAnE,QAAA,CAAAiB,SAAA;QACA,IAAAF,4BAAA,EAAAf,QAAA,EAAAmC,IAAA,WAAA5B,QAAA;UACA4D,MAAA,CAAAnB,MAAA,CAAAG,UAAA;UACAgB,MAAA,CAAA9I,IAAA;UACA8I,MAAA,CAAA1E,OAAA;QACA;MACA;QACA,IAAA2E,yBAAA,EAAApE,QAAA,EAAAmC,IAAA,WAAA5B,QAAA;UACA4D,MAAA,CAAAnB,MAAA,CAAAG,UAAA;UACAgB,MAAA,CAAA9I,IAAA;UACA8I,MAAA,CAAA1E,OAAA;QACA;MACA;IACA;IACA,eACA4E,kBAAA,WAAAA,mBAAAxE,GAAA;MAAA,IAAAyE,MAAA;MAAA,WAAApE,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAkE,SAAA;QAAA,IAAA9I,aAAA,EAAA8E,QAAA,EAAAiE,SAAA;QAAA,WAAApE,oBAAA,CAAAD,OAAA,IAAAO,IAAA,UAAA+D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7D,IAAA,GAAA6D,SAAA,CAAA5D,IAAA;YAAA;cAAA,MAEAjB,GAAA,CAAA8E,KAAA,IAAA9E,GAAA,CAAA3D,YAAA;gBAAAwI,SAAA,CAAA5D,IAAA;gBAAA;cAAA;cAAA4D,SAAA,CAAA5D,IAAA;cAAA,OACAwD,MAAA,CAAAM,qBAAA,CAAA/E,GAAA,CAAA8E,KAAA,EAAA9E,GAAA,CAAA3D,YAAA;YAAA;cAAAT,aAAA,GAAAiJ,SAAA,CAAA1D,IAAA;YAAA;cAEAnB,GAAA,CAAApE,aAAA,GAAAA,aAAA;cACAoE,GAAA,CAAAgF,YAAA,GAAAhF,GAAA,CAAAgF,YAAA,CAAApB,GAAA,WAAAqB,MAAA;gBACA,WAAAC,cAAA,CAAA5E,OAAA,MAAA4E,cAAA,CAAA5E,OAAA,MACA2E,MAAA;kBACAE,gBAAA,EAAAvJ;gBAAA;cAEA;cACA;cAAAiJ,SAAA,CAAA5D,IAAA;cAAA,OACA,IAAAC,4BAAA,EAAAlB,GAAA;YAAA;cAAAU,QAAA,GAAAmE,SAAA,CAAA1D,IAAA;cAAA0D,SAAA,CAAA5D,IAAA;cAAA,OAGA,IAAAI,qCAAA;gBACAD,SAAA,EAAApB,GAAA,CAAAoB,SAAA;gBACAxF,aAAA,EAAAA,aAAA;gBACA6C,aAAA;cACA;YAAA;cAJAkG,SAAA,GAAAE,SAAA,CAAA1D,IAAA;cAMA,IAAAT,QAAA,CAAAY,IAAA,YAAAqD,SAAA,CAAArD,IAAA;gBACAmD,MAAA,CAAAlD,QAAA,CAAAC,OAAA;gBACAiD,MAAA,CAAA7E,OAAA;cACA;YAAA;YAAA;cAAA,OAAAiF,SAAA,CAAAnD,IAAA;UAAA;QAAA,GAAAgD,QAAA;MAAA;IACA;IACA;IACAK,qBAAA,WAAAA,sBAAAD,KAAA,EAAAzI,YAAA;MAAA,WAAAgE,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA4E,SAAA;QAAA,IAAA1E,QAAA;QAAA,WAAAH,oBAAA,CAAAD,OAAA,IAAAO,IAAA,UAAAwE,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtE,IAAA,GAAAsE,SAAA,CAAArE,IAAA;YAAA;cAAAqE,SAAA,CAAAtE,IAAA;cAAAsE,SAAA,CAAArE,IAAA;cAAA,OAGA,IAAAsE,+BAAA,EAAAT,KAAA,EAAAzI,YAAA;YAAA;cAAAqE,QAAA,GAAA4E,SAAA,CAAAnE,IAAA;cAAA,MACAT,QAAA,CAAAY,IAAA;gBAAAgE,SAAA,CAAArE,IAAA;gBAAA;cAAA;cAAA,OAAAqE,SAAA,CAAAE,MAAA,WACA9E,QAAA,CAAA+E,GAAA;YAAA;cAAAH,SAAA,CAAArE,IAAA;cAAA;YAAA;cAAAqE,SAAA,CAAAtE,IAAA;cAAAsE,SAAA,CAAAI,EAAA,GAAAJ,SAAA;cAGAK,OAAA,CAAAlE,KAAA,cAAA6D,SAAA,CAAAI,EAAA;YAAA;cAAA,OAAAJ,SAAA,CAAAE,MAAA,WAGA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAA5D,IAAA;UAAA;QAAA,GAAA0D,QAAA;MAAA;IACA;IACA,eACAQ,iBAAA,WAAAA,kBAAAzF,QAAA;MAAA,IAAA0F,MAAA;MACA;MACA,IAAAzE,SAAA,GAAAjB,QAAA,CAAAiB,SAAA;MACA,IAAAA,SAAA;QACA,IAAAC,qCAAA;UAAAD,SAAA,EAAAA,SAAA;UAAA3C,aAAA;QAAA,GACA6D,IAAA,WAAA5B,QAAA;UACA,IAAAA,QAAA,CAAAY,IAAA;YACAuE,MAAA,CAAAtE,QAAA,CAAAC,OAAA;UACA;YACAqE,MAAA,CAAAtE,QAAA,CAAAE,KAAA,CAAAf,QAAA,CAAA+E,GAAA;UACA;QACA,GACAlC,KAAA,WAAA9B,KAAA;UACAkE,OAAA,CAAAlE,KAAA,iBAAAA,KAAA;UACAoE,MAAA,CAAAtE,QAAA,CAAAE,KAAA;QACA;MACA;;MAEA;MACA,KAAA7B,OAAA;IACA;IACA,aACAkG,YAAA,WAAAA,aAAA9F,GAAA;MAAA,IAAA+F,MAAA;MACA,IAAAC,UAAA,GAAAhG,GAAA,CAAAoB,SAAA,SAAAnG,GAAA;MACA,KAAAkI,MAAA,CAAAC,OAAA,qBAAA4C,UAAA,cAAA1D,IAAA;QACA,WAAA2D,yBAAA,EAAAD,UAAA;MACA,GAAA1D,IAAA;QACAyD,MAAA,CAAAnG,OAAA;QACAmG,MAAA,CAAA5C,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IACA,iBACA2C,mCAAA,WAAAA,oCAAA;MAAA,IAAAC,OAAA;MACA;MACA,SAAAlL,GAAA,CAAA6I,MAAA;QACA,KAAAX,MAAA,CAAAiD,QAAA;QACA;MACA;MAEA,IAAAC,gCAAA,OAAApL,GAAA,EACAqH,IAAA,WAAA5B,QAAA;QACA;QACA,IAAA7F,IAAA,GAAA6F,QAAA;;QAEA;QACA,IAAA4F,QAAA,eAAAC,MAAA,KAAAC,IAAA,GAAAC,OAAA;;QAEA;QACA,IAAAC,IAAA,OAAAC,IAAA,EAAA9L,IAAA;UACA+L,IAAA;QACA;;QAEA;QACA,IAAAC,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAT,IAAA;QACAG,IAAA,CAAAO,IAAA,GAAAJ,GAAA;QACAH,IAAA,CAAAQ,QAAA,GAAAf,QAAA;;QAEA;QACAQ,QAAA,CAAAQ,IAAA,CAAAC,WAAA,CAAAV,IAAA;QACAA,IAAA,CAAAW,KAAA;;QAEA;QACAV,QAAA,CAAAQ,IAAA,CAAAG,WAAA,CAAAZ,IAAA;QACAI,MAAA,CAAAC,GAAA,CAAAQ,eAAA,CAAAV,GAAA;QAEAb,OAAA,CAAAhD,MAAA,CAAAG,UAAA;MACA,GACAC,KAAA,WAAA9B,KAAA;QACAkE,OAAA,CAAAlE,KAAA,YAAAA,KAAA;QACA0E,OAAA,CAAAhD,MAAA,CAAAiD,QAAA;MACA;IACA;IACA,eACAuB,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MAEA,SAAApE,YAAA;QACA,KAAAL,MAAA,CAAAiD,QAAA;QACA;MACA;;MAEA;MACA,SAAAnL,GAAA,CAAA6I,MAAA;QACA,KAAAX,MAAA,CAAAiD,QAAA;QACA;MACA;;MAEA;MACA,IAAAyB,gBAAA,QAAAvM,cAAA,CAAAwM,MAAA,WAAAjE,IAAA;QAAA,OAAA+D,OAAA,CAAA3M,GAAA,CAAA8M,QAAA,CAAAlE,IAAA,CAAAzC,SAAA;MAAA;;MAEA;MACA,IAAA4G,YAAA,GAAAH,gBAAA;MACA,IAAAI,QAAA,GAAAJ,gBAAA,CAAAK,KAAA,WAAAC,OAAA;QACA;QACA,IAAAA,OAAA,CAAA1J,aAAA,mBAAA0J,OAAA,CAAA1J,aAAA;UACA;QACA;QACA;QACA,IAAA0J,OAAA,CAAArM,OAAA,KAAAkM,YAAA,CAAAlM,OAAA;UACA;QACA;QACA;QACA,IAAAqM,OAAA,CAAA9L,YAAA,KAAA2L,YAAA,CAAA3L,YAAA;UACA;QACA;QACA;QACA,IAAA8L,OAAA,CAAAxK,mBAAA,KAAAqK,YAAA,CAAArK,mBAAA;UACA;QACA;QACA;MACA;MAEA,KAAAsK,QAAA;QACA,KAAA9E,MAAA,CAAAiD,QAAA;QACA;MACA;;MAEA;MACA,KAAAjD,MAAA,CAAAC,OAAA,qDAAAmD,MAAA,MAAAtL,GAAA,CAAA6I,MAAA,4LAAAxB,IAAA;QACA,IAAA8F,yBAAA,EAAAR,OAAA,CAAA3M,GAAA,EAAAqH,IAAA,WAAA5B,QAAA;UACA,IAAAA,QAAA,CAAAY,IAAA;YACAsG,OAAA,CAAAzE,MAAA,CAAAG,UAAA;YACAsE,OAAA,CAAAhI,OAAA;UACA;YACAgI,OAAA,CAAAzE,MAAA,CAAAiD,QAAA,CAAA1F,QAAA,CAAA+E,GAAA;UACA;QACA,GAAAlC,KAAA,WAAA9B,KAAA;UACAkE,OAAA,CAAAlE,KAAA,YAAAA,KAAA;UACAmG,OAAA,CAAAzE,MAAA,CAAAiD,QAAA;QACA;MACA,GAAA7C,KAAA;QACA;MAAA,CACA;IACA;IACA,aACA8E,YAAA,WAAAA,aAAA;MACA,KAAAhB,QAAA,iCAAAnC,cAAA,CAAA5E,OAAA,MACA,KAAA7E,WAAA,iBAAA8K,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA6B,OAAA,WAAAA,QAAAC,EAAA;MACA,IAAAA,EAAA;QACA,IAAAC,KAAA,QAAAC,MAAA,CAAAC,KAAA,CAAA7N,IAAA,CAAA8N,cAAA,CAAAb,MAAA,WAAAc,OAAA;UAAA,OAAAA,OAAA,CAAAC,OAAA,IAAAN,EAAA;QAAA;QACA,IAAAC,KAAA;UACA,OAAAA,KAAA,CAAAM,cAAA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IACAjJ,SAAA,WAAAA,UAAA;MAAA,IAAAkJ,OAAA;MACA,SAAAN,MAAA,CAAAC,KAAA,CAAA7N,IAAA,CAAA8N,cAAA,CAAA7E,MAAA,cAAA2E,MAAA,CAAAC,KAAA,CAAA7N,IAAA,CAAAmO,SAAA,CAAAL,cAAA;QACAM,cAAA,CAAAC,QAAA,sBAAA5G,IAAA;UACAyG,OAAA,CAAAI,SAAA,GAAAJ,OAAA,CAAAN,MAAA,CAAAC,KAAA,CAAA7N,IAAA,CAAA8N,cAAA,CAAAb,MAAA,WAAAjE,IAAA;YAAA,OAAAA,IAAA,CAAAuF,IAAA,CAAAC,aAAA;UAAA;QACA;MACA;QACA,KAAAF,SAAA,QAAAV,MAAA,CAAAC,KAAA,CAAA7N,IAAA,CAAA8N,cAAA,CAAAb,MAAA,WAAAjE,IAAA;UAAA,OAAAA,IAAA,CAAAuF,IAAA,CAAAC,aAAA;QAAA;MACA;IACA;EACA;AACA;AAAAC,OAAA,CAAAhJ,OAAA,GAAAiJ,QAAA"}]}