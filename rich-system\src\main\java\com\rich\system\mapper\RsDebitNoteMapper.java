package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsDebitNote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 分账单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Mapper
public interface RsDebitNoteMapper {
    /**
     * 查询分账单
     *
     * @param debitNoteId 分账单主键
     * @return 分账单
     */
    RsDebitNote selectRsDebitNoteByDebitNoteId(Long debitNoteId);

    /**
     * 查询分账单列表
     *
     * @param rsDebitNote 分账单
     * @return 分账单集合
     */
    List<RsDebitNote> selectRsDebitNoteList(RsDebitNote rsDebitNote);

    /**
     * 新增分账单
     *
     * @param rsDebitNote 分账单
     * @return 结果
     */
    int insertRsDebitNote(RsDebitNote rsDebitNote);

    /**
     * 修改分账单
     *
     * @param rsDebitNote 分账单
     * @return 结果
     */
    int updateRsDebitNote(RsDebitNote rsDebitNote);

    /**
     * 删除分账单
     *
     * @param debitNoteId 分账单主键
     * @return 结果
     */
    int deleteRsDebitNoteByDebitNoteId(Long debitNoteId);

    /**
     * 批量删除分账单
     *
     * @param debitNoteIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsDebitNoteByDebitNoteIds(Long[] debitNoteIds);

    List<RsDebitNote> selectRsDebitNoteByRctId(Long rctId);

    void deleteRsDebitNoteByRctId(Long rctId);

    int upsertRsDebitNote(RsDebitNote rsDebitNote);

    /**
     * 按服务ID删除分账单记录
     *
     * @param serviceId 服务ID
     */
    void deleteRsDebitNoteByServiceId(Long serviceId);

    /**
     * 按操作单ID和服务类型删除分账单记录
     *
     * @param rctId          操作单ID
     * @param serviceTypeIds 服务类型ID列表
     * @return 结果
     */
    int deleteRsDebitNoteByRctIdAndServiceTypes(@Param("rctId") Long rctId, @Param("serviceTypeIds") List<Long> serviceTypeIds);

    /**
     * 根据发票ID更新分账单发票状态
     *
     * @param invoiceId     发票ID
     * @param invoiceStatus 发票状态
     * @return 结果
     */
    int updateInvoiceStatusByInvoiceId(@Param("invoiceId") Long invoiceId, @Param("invoiceStatus") String invoiceStatus);

    /**
     * 批量更新账单的belong_invoice_id
     *
     * @param invoiceIds      发票ID列表
     * @param belongInvoiceId 所属发票ID
     * @return 结果
     */
    int updateBelongInvoiceIdByInvoiceIds(@Param("invoiceIds") List<Long> invoiceIds, @Param("belongInvoiceId") Long belongInvoiceId);
}
