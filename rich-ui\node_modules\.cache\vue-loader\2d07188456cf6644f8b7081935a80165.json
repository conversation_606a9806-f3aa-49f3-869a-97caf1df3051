{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\index.vue", "mtime": 1756452458551}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRWYXRpbnZvaWNlLA0KICBjaGFuZ2VTdGF0dXMsDQogIGRlbFZhdGludm9pY2UsDQogIGdldFZhdGludm9pY2UsDQogIGxpc3RWYXRpbnZvaWNlLA0KICB1cGRhdGVWYXRpbnZvaWNlLA0KICBnZW5lcmF0ZUludm9pY2VFeGNlbCwNCiAgbWVyZ2VJbnZvaWNlcywgZ2VuZXJhdGVJbnZvaWNlQ29kZQ0KfSBmcm9tICJAL2FwaS9zeXN0ZW0vdmF0SW52b2ljZSINCmltcG9ydCBzdG9yZSBmcm9tICJAL3N0b3JlIg0KaW1wb3J0IFZhdGludm9pY2VEaWFsb2cgZnJvbSAiLi9jb21wb25lbnRzL1ZhdGludm9pY2VEaWFsb2ciDQppbXBvcnQge3VwZGF0ZURlYml0Tm90ZUJ5SW52b2ljZUlkfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGViaXRub3RlIg0KaW1wb3J0IHtwYXJzZVRpbWV9IGZyb20gIi4uLy4uLy4uL3V0aWxzL3JpY2giDQppbXBvcnQgbW9tZW50IGZyb20gIm1vbWVudCINCmltcG9ydCBjdXJyZW5jeSBmcm9tICJjdXJyZW5jeS5qcyINCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiVmF0aW52b2ljZSIsDQogIGNvbXB1dGVkOiB7DQogICAgbW9tZW50KCkgew0KICAgICAgcmV0dXJuIG1vbWVudA0KICAgIH0NCiAgfSwNCiAgY29tcG9uZW50czogew0KICAgIFZhdGludm9pY2VEaWFsb2cNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2hvd0xlZnQ6IDAsDQogICAgICBzaG93UmlnaHQ6IDI0LA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOWPkeelqOeZu+iusOihqOagvOaVsOaNrg0KICAgICAgdmF0aW52b2ljZUxpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMjAsDQogICAgICAgIGludm9pY2VDb2RlTm86IG51bGwsDQogICAgICAgIGludm9pY2VPZmZpY2FsTm86IG51bGwsDQogICAgICAgIHNhbGVCdXk6IG51bGwsDQogICAgICAgIGludm9pY2VCZWxvbmdzVG86IG51bGwsDQogICAgICAgIHJpY2hCYW5rQ29kZTogbnVsbCwNCiAgICAgICAgcmljaENvbXBhbnlUaXRsZTogbnVsbCwNCiAgICAgICAgcmljaFZhdFNlcmlhbE5vOiBudWxsLA0KICAgICAgICByaWNoQmFua0FjY291bnQ6IG51bGwsDQogICAgICAgIHJpY2hCYW5rRnVsbG5hbWU6IG51bGwsDQogICAgICAgIGNvb3BlcmF0b3JJZDogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvclNob3J0TmFtZTogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvckJhbmtDb2RlOiBudWxsLA0KICAgICAgICBjb29wZXJhdG9yRnVsbG5hbWU6IG51bGwsDQogICAgICAgIGNvb3BlcmF0b3JWYXRTZXJpYWxObzogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvckJhbmtBY2NvdW50OiBudWxsLA0KICAgICAgICBjb29wZXJhdG9yQmFua0Z1bGxuYW1lOiBudWxsLA0KICAgICAgICByY3ROb1N1bW1hcnk6IG51bGwsDQogICAgICAgIGNvb3BlcmF0b3JSZWZlck5vOiBudWxsLA0KICAgICAgICBvZmZpY2FsQ2hhcmdlTmFtZVN1bW1hcnk6IG51bGwsDQogICAgICAgIGNoYXJnZUN1cnJlbmN5Q29kZTogbnVsbCwNCiAgICAgICAgZG5TdW06IG51bGwsDQogICAgICAgIGRuUmVjaWV2ZWQ6IG51bGwsDQogICAgICAgIGRuQmFsYW5jZTogbnVsbCwNCiAgICAgICAgY25TdW06IG51bGwsDQogICAgICAgIGNuUGFpZDogbnVsbCwNCiAgICAgICAgY25CYWxhbmNlOiBudWxsLA0KICAgICAgICBjaGFyZ2VDbGVhclN0YXR1czogbnVsbCwNCiAgICAgICAgZXhwZWN0ZWRQYXlEYXRlOiBudWxsLA0KICAgICAgICBhcHByb3ZlZFBheURhdGU6IG51bGwsDQogICAgICAgIGFjdHVhbFBheURhdGU6IG51bGwsDQogICAgICAgIHNxZEJhbmtTdGF0ZW1lbnRMaXN0OiBudWxsLA0KICAgICAgICBpbnZvaWNlQ3VycmVuY3lDb2RlOiBudWxsLA0KICAgICAgICBpbnZvaWNlRXhjaGFuZ2VSYXRlOiBudWxsLA0KICAgICAgICBpbnZvaWNlTmV0QW1vdW50OiBudWxsLA0KICAgICAgICB2YXRBbW91bnQ6IG51bGwsDQogICAgICAgIGludm9pY2VWYXRBbW91bnQ6IG51bGwsDQogICAgICAgIHNhbGVOZXRTdW06IG51bGwsDQogICAgICAgIHNhbGVUYXg6IG51bGwsDQogICAgICAgIHNhbGVUYXhUb3RhbDogbnVsbCwNCiAgICAgICAgYnV5TmV0U3VtOiBudWxsLA0KICAgICAgICBidXlUYXg6IG51bGwsDQogICAgICAgIGJ1eVRheFRvdGFsOiBudWxsLA0KICAgICAgICB0YXhDbGFzczogbnVsbCwNCiAgICAgICAgaW52b2ljZVR5cGU6IG51bGwsDQogICAgICAgIGJlbG9uZ3NUb01vbnRoOiBudWxsLA0KICAgICAgICBpbnZvaWNlU3RhdHVzOiBudWxsLA0KICAgICAgICBpbnZvaWNlUmVtYXJrOiBudWxsLA0KICAgICAgICBhcHBseVN0dWZmSWQ6IG51bGwsDQogICAgICAgIGFwcGxpZWRUaW1lOiBudWxsLA0KICAgICAgICBpc3N1ZWRTdHVmZklkOiBudWxsLA0KICAgICAgICBpc3N1ZWRUaW1lOiBudWxsLA0KICAgICAgICB0YXhTdHVmZklkOiBudWxsLA0KICAgICAgICB0YXhEZWNsYXJlVGltZTogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGludm9pY2VDb2RlTm86IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlj5HnpajmtYHmsLTlj7fkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBzYWxlQnV5OiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiAi6L+b6ZSA5qCH5b+X77yac2FsZT3plIDpobnvvIxidXk96L+b6aG55LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIg0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIGludm9pY2VJdGVtTGlzdDogW10sDQogICAgICBjb21wYW55TGlzdDogW10sDQogICAgICBiYW5rQWNjb3VudExpc3Q6IFtdDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgIHNob3dTZWFyY2gobikgew0KICAgICAgaWYgKG4gPT09IHRydWUpIHsNCiAgICAgICAgdGhpcy5zaG93UmlnaHQgPSAyMQ0KICAgICAgICB0aGlzLnNob3dMZWZ0ID0gMw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zaG93UmlnaHQgPSAyNA0KICAgICAgICB0aGlzLnNob3dMZWZ0ID0gMA0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKQ0KICAgIHRoaXMubG9hZFN0YWZmKCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIHNlbGVjdGFibGVSb3cocm93KSB7DQogICAgICByZXR1cm4gcm93LmF1ZGl0U3R1ZmZJZCAhPT0gbnVsbA0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlSW52b2ljZUF1ZGl0Q2FuY2VsKGZvcm1EYXRhKSB7DQogICAgICAvLyDkv67mlLnlj5HnpagNCiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdXBkYXRlVmF0aW52b2ljZShmb3JtRGF0YSkNCiAgICAgIC8vIOabtOaWsOWIhui0puWNlQ0KICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHsNCiAgICAgICAgaW52b2ljZUlkOiBmb3JtRGF0YS5pbnZvaWNlSWQsDQogICAgICAgIGludm9pY2VTdGF0dXM6ICJ1bmlzc3VlZCINCiAgICAgIH0NCiAgICAgIGNvbnN0IHVwZGF0ZVJlc3BvbnNlID0gYXdhaXQgdXBkYXRlRGViaXROb3RlQnlJbnZvaWNlSWQodXBkYXRlRGF0YSkNCiAgICAgIGlmICh1cGRhdGVSZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Y+W5raI5a6h5qC45oiQ5YqfIikNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWPlua2iOWuoeaguOWksei0pSIpDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVJbnZvaWNlQXBwbHlDYW5jZWwoZm9ybURhdGEpIHsNCiAgICAgIC8vIOS/ruaUueWPkeelqA0KICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB1cGRhdGVWYXRpbnZvaWNlKGZvcm1EYXRhKQ0KICAgICAgLy8g5pu05paw5YiG6LSm5Y2VDQogICAgICBjb25zdCB1cGRhdGVEYXRhID0gew0KICAgICAgICBpbnZvaWNlSWQ6IGZvcm1EYXRhLmludm9pY2VJZCwNCiAgICAgICAgaW52b2ljZVN0YXR1czogInVuaXNzdWVkIg0KICAgICAgfQ0KICAgICAgY29uc3QgdXBkYXRlUmVzcG9uc2UgPSBhd2FpdCB1cGRhdGVEZWJpdE5vdGVCeUludm9pY2VJZCh1cGRhdGVEYXRhKQ0KICAgICAgaWYgKHVwZGF0ZVJlc3BvbnNlLmNvZGUgPT09IDIwMCAmJiByZXNwb25zZS5jb2RlID09PSAyMDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlj5bmtojnlLPor7fmiJDlip8iKQ0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Y+W5raI55Sz6K+35aSx6LSlIikNCiAgICAgIH0NCiAgICB9LA0KICAgIGN1cnJlbmN5LA0KICAgIHBhcnNlVGltZSwNCiAgICBoYW5kbGVSb3dDbGljayhyb3cpIHsNCiAgICAgIHRoaXMuaGFuZGxlVXBkYXRlKHJvdykNCiAgICB9LA0KICAgIC8qKiDmn6Xor6Llj5HnpajnmbvorrDliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgLy8g5LiN5pi+56S65pyq55Sz6K+35byA56Wo55qE5Y+R56WoDQogICAgICBsaXN0VmF0aW52b2ljZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy52YXRpbnZvaWNlTGlzdCA9IHJlc3BvbnNlLnJvd3MNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGludm9pY2VJZDogbnVsbCwNCiAgICAgICAgaW52b2ljZUNvZGVObzogbnVsbCwNCiAgICAgICAgaW52b2ljZU9mZmljYWxObzogbnVsbCwNCiAgICAgICAgc2FsZUJ1eTogbnVsbCwNCiAgICAgICAgaW52b2ljZUJlbG9uZ3NUbzogbnVsbCwNCiAgICAgICAgcmljaEJhbmtDb2RlOiBudWxsLA0KICAgICAgICByaWNoQ29tcGFueVRpdGxlOiBudWxsLA0KICAgICAgICByaWNoVmF0U2VyaWFsTm86IG51bGwsDQogICAgICAgIHJpY2hCYW5rQWNjb3VudDogbnVsbCwNCiAgICAgICAgcmljaEJhbmtGdWxsbmFtZTogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvcklkOiBudWxsLA0KICAgICAgICBjb29wZXJhdG9yU2hvcnROYW1lOiBudWxsLA0KICAgICAgICBjb29wZXJhdG9yQmFua0NvZGU6IG51bGwsDQogICAgICAgIGNvb3BlcmF0b3JGdWxsbmFtZTogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvclZhdFNlcmlhbE5vOiBudWxsLA0KICAgICAgICBjb29wZXJhdG9yQmFua0FjY291bnQ6IG51bGwsDQogICAgICAgIGNvb3BlcmF0b3JCYW5rRnVsbG5hbWU6IG51bGwsDQogICAgICAgIHJjdE5vU3VtbWFyeTogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvclJlZmVyTm86IG51bGwsDQogICAgICAgIG9mZmljYWxDaGFyZ2VOYW1lU3VtbWFyeTogbnVsbCwNCiAgICAgICAgY2hhcmdlQ3VycmVuY3lDb2RlOiBudWxsLA0KICAgICAgICBkblN1bTogbnVsbCwNCiAgICAgICAgZG5SZWNpZXZlZDogbnVsbCwNCiAgICAgICAgZG5CYWxhbmNlOiBudWxsLA0KICAgICAgICBjblN1bTogbnVsbCwNCiAgICAgICAgY25QYWlkOiBudWxsLA0KICAgICAgICBjbkJhbGFuY2U6IG51bGwsDQogICAgICAgIGNoYXJnZUNsZWFyU3RhdHVzOiAiMCIsDQogICAgICAgIGV4cGVjdGVkUGF5RGF0ZTogbnVsbCwNCiAgICAgICAgYXBwcm92ZWRQYXlEYXRlOiBudWxsLA0KICAgICAgICBhY3R1YWxQYXlEYXRlOiBudWxsLA0KICAgICAgICBzcWRCYW5rU3RhdGVtZW50TGlzdDogbnVsbCwNCiAgICAgICAgaW52b2ljZUN1cnJlbmN5Q29kZTogbnVsbCwNCiAgICAgICAgaW52b2ljZUV4Y2hhbmdlUmF0ZTogbnVsbCwNCiAgICAgICAgaW52b2ljZU5ldEFtb3VudDogbnVsbCwNCiAgICAgICAgdmF0QW1vdW50OiBudWxsLA0KICAgICAgICBpbnZvaWNlVmF0QW1vdW50OiBudWxsLA0KICAgICAgICBzYWxlTmV0U3VtOiBudWxsLA0KICAgICAgICBzYWxlVGF4OiBudWxsLA0KICAgICAgICBzYWxlVGF4VG90YWw6IG51bGwsDQogICAgICAgIGJ1eU5ldFN1bTogbnVsbCwNCiAgICAgICAgYnV5VGF4OiBudWxsLA0KICAgICAgICBidXlUYXhUb3RhbDogbnVsbCwNCiAgICAgICAgdGF4Q2xhc3M6IG51bGwsDQogICAgICAgIGludm9pY2VUeXBlOiBudWxsLA0KICAgICAgICBiZWxvbmdzVG9Nb250aDogbnVsbCwNCiAgICAgICAgaW52b2ljZVN0YXR1czogIjAiLA0KICAgICAgICBpbnZvaWNlUmVtYXJrOiBudWxsLA0KICAgICAgICBhcHBseVN0dWZmSWQ6IG51bGwsDQogICAgICAgIGFwcGxpZWRUaW1lOiBudWxsLA0KICAgICAgICBpc3N1ZWRTdHVmZklkOiBudWxsLA0KICAgICAgICBpc3N1ZWRUaW1lOiBudWxsLA0KICAgICAgICB0YXhTdHVmZklkOiBudWxsLA0KICAgICAgICB0YXhEZWNsYXJlVGltZTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbA0KICAgICAgfQ0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKQ0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQ0KICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsNCiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIg0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgi56Gu6K6k6KaBXCIiICsgdGV4dCArICLlkJfvvJ8iKS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGNoYW5nZVN0YXR1cyhyb3cuaW52b2ljZUlkLCByb3cuc3RhdHVzKQ0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKQ0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgew0KICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5Yik5pat6KGM5piv5ZCm5Y+v6YCJDQogICAgaXNTZWxlY3RhYmxlKHJvdykgew0KICAgICAgLy8g5Y+R56Wo54q25oCB5Li6bWVyZ2Vk55qE6KGM5LiN5Y+v5Yu+6YCJDQogICAgICByZXR1cm4gcm93Lmludm9pY2VTdGF0dXMgIT09ICJtZXJnZWQiICYmIHJvdy5tZXJnZUludm9pY2UgPT09ICIxIg0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pbnZvaWNlSWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDlj5HnpajnmbvorrAiDQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpDQogICAgICBjb25zdCBpbnZvaWNlSWQgPSByb3cuaW52b2ljZUlkIHx8IHRoaXMuaWRzDQogICAgICBnZXRWYXRpbnZvaWNlKGludm9pY2VJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgdGhpcy5jb21wYW55TGlzdCA9IHJlc3BvbnNlLmV4dENvbXBhbnlMaXN0DQogICAgICAgIHRoaXMuYmFua0FjY291bnRMaXN0ID0gcmVzcG9uc2UuYmFzQWNjb3VudExpc3QNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueWPkeelqOeZu+iusCINCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIC8vIOW3suenu+iHs2hhbmRsZURpYWxvZ1N1Ym1pdOaWueazlQ0KICAgIH0sDQoNCiAgICAvKiog5aSE55CG5a+56K+d5qGG5o+Q5LqkICovDQogICAgaGFuZGxlRGlhbG9nU3VibWl0KGZvcm1EYXRhKSB7DQogICAgICBpZiAoZm9ybURhdGEuaW52b2ljZUlkICE9IG51bGwpIHsNCiAgICAgICAgdXBkYXRlVmF0aW52b2ljZShmb3JtRGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIikNCiAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICBhZGRWYXRpbnZvaWNlKGZvcm1EYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKQ0KICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDlpITnkIblrqHmoLjlj5Hnpajkuovku7YgKi8NCiAgICBhc3luYyBoYW5kbGVBdWRpdEludm9pY2Uocm93KSB7DQogICAgICBsZXQgaW52b2ljZUNvZGVObw0KICAgICAgaWYgKHJvdy5yY3RJZCAmJiByb3cuY29vcGVyYXRvcklkKSB7DQogICAgICAgIGludm9pY2VDb2RlTm8gPSBhd2FpdCB0aGlzLmdlbmVyYXRlSW52b2ljZUNvZGVObyhyb3cucmN0SWQsIHJvdy5jb29wZXJhdG9ySWQpDQogICAgICB9DQogICAgICByb3cuaW52b2ljZUNvZGVObyA9IGludm9pY2VDb2RlTm8NCiAgICAgIHJvdy5yc0NoYXJnZUxpc3QgPSByb3cucnNDaGFyZ2VMaXN0Lm1hcChjaGFyZ2UgPT4gew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIC4uLmNoYXJnZSwNCiAgICAgICAgICBzcWRJbnZvaWNlQ29kZU5vOiBpbnZvaWNlQ29kZU5vDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICAvLyDosIPnlKhBUEnmm7TmlrDlj5HnpajlrqHmoLjkv6Hmga8NCiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdXBkYXRlVmF0aW52b2ljZShyb3cpDQoNCiAgICAgIC8vIOabtOaWsOi0puWNleWPkeelqOS/oeaBry0+5bey5a6h5qC4DQogICAgICBjb25zdCByZXNwb25zZTIgPSBhd2FpdCB1cGRhdGVEZWJpdE5vdGVCeUludm9pY2VJZCh7DQogICAgICAgIGludm9pY2VJZDogcm93Lmludm9pY2VJZCwNCiAgICAgICAgaW52b2ljZUNvZGVObzogaW52b2ljZUNvZGVObywNCiAgICAgICAgaW52b2ljZVN0YXR1czogImNvbmZpcm1lZCINCiAgICAgIH0pDQoNCiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UyLmNvZGUgPT09IDIwMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuS/oeaBr+WuoeaguOaIkOWKnyIpDQogICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICB9DQogICAgfSwNCiAgICAvLyDnlJ/miJDlj5HnpajmtYHmsLTlj7cNCiAgICBhc3luYyBnZW5lcmF0ZUludm9pY2VDb2RlTm8ocmN0SWQsIGNvb3BlcmF0b3JJZCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6LCD55SoQVBJ55Sf5oiQ5Y+R56Wo57yW56CBDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2VuZXJhdGVJbnZvaWNlQ29kZShyY3RJZCwgY29vcGVyYXRvcklkKQ0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgcmV0dXJuIHJlc3BvbnNlLm1zZw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLnlJ/miJDlj5HnpajnvJbnoIHlpLHotKU6IiwgZXJyb3IpDQogICAgICB9DQogICAgICAvLyDlpoLmnpxBUEnosIPnlKjlpLHotKXvvIzov5Tlm57pu5jorqTmoLzlvI8NCiAgICAgIHJldHVybiAiIg0KICAgIH0sDQogICAgLyoqIOWkhOeQhuWPkemAgeW8gOelqOS6i+S7tiAqLw0KICAgIGhhbmRsZVNlbmRJbnZvaWNlKGZvcm1EYXRhKSB7DQogICAgICAvLyDmm7TmlrBkZWJpdE5vdGXkuLrlt7LlvIDnpagNCiAgICAgIGNvbnN0IGludm9pY2VJZCA9IGZvcm1EYXRhLmludm9pY2VJZA0KICAgICAgaWYgKGludm9pY2VJZCkgew0KICAgICAgICB1cGRhdGVEZWJpdE5vdGVCeUludm9pY2VJZCh7aW52b2ljZUlkLCBpbnZvaWNlU3RhdHVzOiAiaXNzdWVkIn0pDQogICAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWIhui0puWNleWPkeelqOeKtuaAgeabtOaWsOaIkOWKnyIpDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKHJlc3BvbnNlLm1zZyB8fCAi5YiG6LSm5Y2V5Y+R56Wo54q25oCB5pu05paw5aSx6LSlIikNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgICBjb25zb2xlLmVycm9yKCLliIbotKbljZXlj5HnpajnirbmgIHmm7TmlrDlpLHotKU6IiwgZXJyb3IpDQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliIbotKbljZXlj5HnpajnirbmgIHmm7TmlrDlpLHotKXvvIzor7fph43or5UiKQ0KICAgICAgICAgIH0pDQogICAgICB9DQoNCiAgICAgIC8vIOWIt+aWsOWIl+ihqA0KICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBpbnZvaWNlSWRzID0gcm93Lmludm9pY2VJZCB8fCB0aGlzLmlkcw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgi5piv5ZCm56Gu6K6k5Yig6Zmk5Y+R56Wo55m76K6w57yW5Y+35Li6XCIiICsgaW52b2ljZUlkcyArICJcIueahOaVsOaNrumhue+8nyIpLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gZGVsVmF0aW52b2ljZShpbnZvaWNlSWRzKQ0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWvvOWHuuW8gOelqOi1hOaWmeaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydFRoZUludm9pY2luZ0luZm9ybWF0aW9uKCkgew0KICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ6YCJ5Lit55qE5Y+R56WoDQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7flhYjpgInmi6nopoHlr7zlh7rnmoTlj5HnpagiKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgZ2VuZXJhdGVJbnZvaWNlRXhjZWwodGhpcy5pZHMpDQogICAgICAgIC50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAvLyDojrflj5bmlofku7bnmoTlrZfoioLmlbDnu4QgKEFycmF5QnVmZmVyKQ0KICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZQ0KDQogICAgICAgICAgLy8g55Sf5oiQ5paH5Lu25ZCNDQogICAgICAgICAgbGV0IGZpbGVOYW1lID0gYEludm9pY2VzXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgDQoNCiAgICAgICAgICAvLyDliJvlu7rkuIDkuKogQmxvYiDlr7nosaHmnaXlrZjlgqjmlofku7YNCiAgICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2RhdGFdLCB7DQogICAgICAgICAgICB0eXBlOiAiYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LnNwcmVhZHNoZWV0bWwuc2hlZXQiICAvLyBFeGNlbCDmlofku7bnsbvlnosNCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgLy8g5Yib5bu65LiA5Liq5Li05pe26ZO+5o6l77yM5qih5ouf54K55Ye75p2l5LiL6L295paH5Lu2DQogICAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKQ0KICAgICAgICAgIGNvbnN0IHVybCA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpICAvLyDliJvlu7rkuIDkuKogVVJMIOaMh+WQkSBCbG9iIOWvueixoQ0KICAgICAgICAgIGxpbmsuaHJlZiA9IHVybA0KICAgICAgICAgIGxpbmsuZG93bmxvYWQgPSBmaWxlTmFtZSAgLy8g6K6+572u5LiL6L2955qE5paH5Lu25ZCNDQoNCiAgICAgICAgICAvLyDmqKHmi5/ngrnlh7vpk77mjqXvvIzop6blj5HkuIvovb0NCiAgICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGxpbmspDQogICAgICAgICAgbGluay5jbGljaygpDQoNCiAgICAgICAgICAvLyDkuIvovb3lrozmiJDlkI7np7vpmaTpk77mjqXvvIzlubbph4rmlL4gVVJMIOWvueixoQ0KICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluaykNCiAgICAgICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTCh1cmwpDQoNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCJFeGNlbOaWh+S7tuWvvOWHuuaIkOWKnyIpDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcigi5paH5Lu25LiL6L295aSx6LSlOiIsIGVycm9yKQ0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCJFeGNlbOaWh+S7tuWvvOWHuuWksei0pSIpDQogICAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5ZCI5bm25Y+R56Wo5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlTWVyZ2VJbnZvaWNlcygpIHsNCg0KICAgICAgaWYgKHRoaXMuaXNTZWxlY3RhYmxlKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLpgInkuK3nmoTlj5HnpajmnInkuI3og73lkIjlubbnmoTpobnnm64iKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ6YCJ5Lit55qE5Y+R56WoDQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoIDwgMikgew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+36Iez5bCR6YCJ5oupMuW8oOWPkeelqOi/m+ihjOWQiOW5tiIpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDojrflj5bpgInkuK3nmoTlj5HnpajmlbDmja4NCiAgICAgIGNvbnN0IHNlbGVjdGVkSW52b2ljZXMgPSB0aGlzLnZhdGludm9pY2VMaXN0LmZpbHRlcihpdGVtID0+IHRoaXMuaWRzLmluY2x1ZGVzKGl0ZW0uaW52b2ljZUlkKSkNCg0KICAgICAgLy8g5qOA5p+l5Y+R56Wo54q25oCB5ZKM57G75Z6L5piv5ZCm5LiA6Ie0DQogICAgICBjb25zdCBmaXJzdEludm9pY2UgPSBzZWxlY3RlZEludm9pY2VzWzBdDQogICAgICBjb25zdCBjYW5NZXJnZSA9IHNlbGVjdGVkSW52b2ljZXMuZXZlcnkoaW52b2ljZSA9PiB7DQogICAgICAgIC8vIOajgOafpeWPkeelqOeKtuaAgSAtIOWPquiDveWQiOW5tuacquW8gOelqOeahOWPkeelqA0KICAgICAgICBpZiAoaW52b2ljZS5pbnZvaWNlU3RhdHVzICE9PSAidW5pc3N1ZWQiICYmIGludm9pY2UuaW52b2ljZVN0YXR1cyAhPT0gImFwcGxpZWQiKSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCiAgICAgICAgLy8g5qOA5p+l6L+b6ZSA6aG55piv5ZCm5LiA6Ie0DQogICAgICAgIGlmIChpbnZvaWNlLnNhbGVCdXkgIT09IGZpcnN0SW52b2ljZS5zYWxlQnV5KSB7DQogICAgICAgICAgcmV0dXJuIGZhbHNlDQogICAgICAgIH0NCiAgICAgICAgLy8g5qOA5p+l5a+55pa55YWs5Y+45piv5ZCm5LiA6Ie0DQogICAgICAgIGlmIChpbnZvaWNlLmNvb3BlcmF0b3JJZCAhPT0gZmlyc3RJbnZvaWNlLmNvb3BlcmF0b3JJZCkgew0KICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICB9DQogICAgICAgIC8vIOajgOafpeW4geenjeaYr+WQpuS4gOiHtA0KICAgICAgICBpZiAoaW52b2ljZS5pbnZvaWNlQ3VycmVuY3lDb2RlICE9PSBmaXJzdEludm9pY2UuaW52b2ljZUN1cnJlbmN5Q29kZSkgew0KICAgICAgICAgIHJldHVybiBmYWxzZQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9KQ0KDQogICAgICBpZiAoIWNhbk1lcmdlKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLlj6rog73lkIjlubbnirbmgIHkuLrmnKrlvIDnpajmiJblt7LnlLPor7fjgIHov5vplIDpobnkuIDoh7TjgIHlr7nmlrnlhazlj7jkuIDoh7TjgIHluIHnp43kuIDoh7TnmoTlj5HnpagiKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g56Gu6K6k5ZCI5bm25pON5L2cDQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKGDnoa7orqTopoHlkIjlubbpgInkuK3nmoQgJHt0aGlzLmlkcy5sZW5ndGh9IOW8oOWPkeelqOWQl++8n+WQiOW5tuWQjuWwhueUn+aIkOS4gOW8oOaWsOeahOWPkeelqO+8jOWOn+WPkeelqOWwhuiiq+agh+iusOS4uuW3suWQiOW5tuOAgmApLnRoZW4oKCkgPT4gew0KICAgICAgICBtZXJnZUludm9pY2VzKHRoaXMuaWRzKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlj5HnpajlkIjlubbmiJDlip8iKQ0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkgLy8g5Yi35paw5YiX6KGoDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKHJlc3BvbnNlLm1zZyB8fCAi5Y+R56Wo5ZCI5bm25aSx6LSlIikNCiAgICAgICAgICB9DQogICAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLlj5HnpajlkIjlubblpLHotKU6IiwgZXJyb3IpDQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuWPkeelqOWQiOW5tuWksei0pe+8jOivt+mHjeivlSIpDQogICAgICAgIH0pDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgIC8vIOeUqOaIt+WPlua2iOaTjeS9nA0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCJzeXN0ZW0vdmF0aW52b2ljZS9leHBvcnQiLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGB2YXRpbnZvaWNlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgZ2V0TmFtZShpZCkgew0KICAgICAgaWYgKGlkKSB7DQogICAgICAgIGxldCBzdGFmZiA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IGlkKVswXQ0KICAgICAgICBpZiAoc3RhZmYpIHsNCiAgICAgICAgICByZXR1cm4gc3RhZmYuc3RhZmZTaG9ydE5hbWUNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICByZXR1cm4gIiINCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuICIiDQogICAgICB9DQogICAgfSwNCiAgICBsb2FkU3RhZmYoKSB7DQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5hbGxSc1N0YWZmTGlzdCkgew0KICAgICAgICBzdG9yZS5kaXNwYXRjaCgiZ2V0QWxsUnNTdGFmZkxpc3QiKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLnN0YWZmTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbS5kZXB0LmRlcHRMb2NhbE5hbWUgPT09ICLkuJrliqHpg6giKQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zdGFmZkxpc3QgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmFsbFJzU3RhZmZMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0uZGVwdC5kZXB0TG9jYWxOYW1lID09PSAi5Lia5Yqh6YOoIikNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2i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file": "index.vue", "sourceRoot": "src/views/system/vatinvoice", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 label-width=\"68px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"发票流水号\" prop=\"invoiceCodeNo\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceCodeNo\"\r\n              clearable\r\n              placeholder=\"发票流水号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票号码\" prop=\"invoiceOfficalNo\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceOfficalNo\"\r\n              clearable\r\n              placeholder=\"发票号码\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属公司\" prop=\"invoiceBelongsTo\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceBelongsTo\"\r\n              clearable\r\n              placeholder=\"所属公司\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司账户简称\" prop=\"richBankCode\">\r\n            <el-input\r\n              v-model=\"queryParams.richBankCode\"\r\n              clearable\r\n              placeholder=\"我司账户简称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司发票抬头\" prop=\"richCompanyTitle\">\r\n            <el-input\r\n              v-model=\"queryParams.richCompanyTitle\"\r\n              clearable\r\n              placeholder=\"我司发票抬头\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司纳税人识别号\" prop=\"richVatSerialNo\">\r\n            <el-input\r\n              v-model=\"queryParams.richVatSerialNo\"\r\n              clearable\r\n              placeholder=\"我司纳税人识别号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司账号\" prop=\"richBankAccount\">\r\n            <el-input\r\n              v-model=\"queryParams.richBankAccount\"\r\n              clearable\r\n              placeholder=\"我司账号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司银行全称\" prop=\"richBankFullname\">\r\n            <el-input\r\n              v-model=\"queryParams.richBankFullname\"\r\n              clearable\r\n              placeholder=\"我司银行全称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方公司ID\" prop=\"cooperatorId\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorId\"\r\n              clearable\r\n              placeholder=\"对方公司ID\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方公司简称\" prop=\"cooperatorShortName\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorShortName\"\r\n              clearable\r\n              placeholder=\"对方公司简称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方账户简称\" prop=\"cooperatorBankCode\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorBankCode\"\r\n              clearable\r\n              placeholder=\"对方账户简称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方发票抬头\" prop=\"cooperatorCompanyTitle\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorCompanyTitle\"\r\n              clearable\r\n              placeholder=\"对方发票抬头\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方纳税人识别号\" prop=\"cooperatorVatSerialNo\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorVatSerialNo\"\r\n              clearable\r\n              placeholder=\"对方纳税人识别号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方账号\" prop=\"cooperatorBankAccount\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorBankAccount\"\r\n              clearable\r\n              placeholder=\"对方账号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方银行全称\" prop=\"cooperatorBankFullname\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorBankFullname\"\r\n              clearable\r\n              placeholder=\"对方银行全称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"结算币种\" prop=\"chargeCurrencyCode\">\r\n            <el-input\r\n              v-model=\"queryParams.chargeCurrencyCode\"\r\n              clearable\r\n              placeholder=\"结算币种\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"账单应收总额\" prop=\"dnSum\">\r\n            <el-input\r\n              v-model=\"queryParams.dnSum\"\r\n              clearable\r\n              placeholder=\"账单应收总额\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行已收\" prop=\"dnRecieved\">\r\n            <el-input\r\n              v-model=\"queryParams.dnRecieved\"\r\n              clearable\r\n              placeholder=\"银行已收\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行未收\" prop=\"dnBalance\">\r\n            <el-input\r\n              v-model=\"queryParams.dnBalance\"\r\n              clearable\r\n              placeholder=\"银行未收\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"账单应付总额\" prop=\"cnSum\">\r\n            <el-input\r\n              v-model=\"queryParams.cnSum\"\r\n              clearable\r\n              placeholder=\"账单应付总额\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行已付\" prop=\"cnPaid\">\r\n            <el-input\r\n              v-model=\"queryParams.cnPaid\"\r\n              clearable\r\n              placeholder=\"银行已付\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行未付\" prop=\"cnBalance\">\r\n            <el-input\r\n              v-model=\"queryParams.cnBalance\"\r\n              clearable\r\n              placeholder=\"银行未付\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"期望支付日期\" prop=\"expectedPayDate\">\r\n            <el-date-picker v-model=\"queryParams.expectedPayDate\"\r\n                            clearable\r\n                            placeholder=\"期望支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"批复支付日期\" prop=\"approvedPayDate\">\r\n            <el-date-picker v-model=\"queryParams.approvedPayDate\"\r\n                            clearable\r\n                            placeholder=\"批复支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"实际支付日期\" prop=\"actualPayDate\">\r\n            <el-date-picker v-model=\"queryParams.actualPayDate\"\r\n                            clearable\r\n                            placeholder=\"实际支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"发票币种\" prop=\"invoiceCurrencyCode\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceCurrencyCode\"\r\n              clearable\r\n              placeholder=\"发票币种\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票汇率\" prop=\"invoiceExchangeRate\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceExchangeRate\"\r\n              clearable\r\n              placeholder=\"发票汇率\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"不含税金额\" prop=\"invoiceNetAmount\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceNetAmount\"\r\n              clearable\r\n              placeholder=\"不含税金额\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"税金\" prop=\"vatAmount\">\r\n            <el-input\r\n              v-model=\"queryParams.vatAmount\"\r\n              clearable\r\n              placeholder=\"税金\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"价税合计\" prop=\"invoiceVatAmount\">\r\n            <el-input\r\n              v-model=\"queryParams.invoiceVatAmount\"\r\n              clearable\r\n              placeholder=\"价税合计\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项不含税\" prop=\"saleNetSum\">\r\n            <el-input\r\n              v-model=\"queryParams.saleNetSum\"\r\n              clearable\r\n              placeholder=\"销项不含税\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项税金\" prop=\"saleTax\">\r\n            <el-input\r\n              v-model=\"queryParams.saleTax\"\r\n              clearable\r\n              placeholder=\"销项税金\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项含税合计\" prop=\"saleTaxTotal\">\r\n            <el-input\r\n              v-model=\"queryParams.saleTaxTotal\"\r\n              clearable\r\n              placeholder=\"销项含税合计\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项不含税\" prop=\"buyNetSum\">\r\n            <el-input\r\n              v-model=\"queryParams.buyNetSum\"\r\n              clearable\r\n              placeholder=\"进项不含税\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项税金\" prop=\"buyTax\">\r\n            <el-input\r\n              v-model=\"queryParams.buyTax\"\r\n              clearable\r\n              placeholder=\"进项税金\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项含税合计\" prop=\"buyTaxTotal\">\r\n            <el-input\r\n              v-model=\"queryParams.buyTaxTotal\"\r\n              clearable\r\n              placeholder=\"进项含税合计\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票性质\" prop=\"taxClass\">\r\n            <el-input\r\n              v-model=\"queryParams.taxClass\"\r\n              clearable\r\n              placeholder=\"发票性质\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"报税所属月份\" prop=\"belongsToMonth\">\r\n            <el-input\r\n              v-model=\"queryParams.belongsToMonth\"\r\n              clearable\r\n              placeholder=\"报税所属月份\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"申请人ID\" prop=\"applyStuffId\">\r\n            <el-input\r\n              v-model=\"queryParams.applyStuffId\"\r\n              clearable\r\n              placeholder=\"申请人ID\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"申请时间\" prop=\"appliedTime\">\r\n            <el-date-picker v-model=\"queryParams.appliedTime\"\r\n                            clearable\r\n                            placeholder=\"申请时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"开票人ID\" prop=\"issuedStuffId\">\r\n            <el-input\r\n              v-model=\"queryParams.issuedStuffId\"\r\n              clearable\r\n              placeholder=\"开票人ID\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"开票时间\" prop=\"issuedTime\">\r\n            <el-date-picker v-model=\"queryParams.issuedTime\"\r\n                            clearable\r\n                            placeholder=\"开票时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"报税人ID\" prop=\"taxStuffId\">\r\n            <el-input\r\n              v-model=\"queryParams.taxStuffId\"\r\n              clearable\r\n              placeholder=\"报税人ID\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"报税时间\" prop=\"taxDeclareTime\">\r\n            <el-date-picker v-model=\"queryParams.taxDeclareTime\"\r\n                            clearable\r\n                            placeholder=\"报税时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:edit']\"\r\n              :disabled=\"single\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleUpdate\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <!-- <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col> -->\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExportTheInvoicingInformation\"\r\n            >导出开票资料\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:add']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-connection\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"info\"\r\n              @click=\"handleMergeInvoices\"\r\n            >合并发票\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"vatinvoiceList\" @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"handleRowClick\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" :selectable=\"selectableRow\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"发票流水号\" prop=\"invoiceCodeNo\" width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"发票号码\" prop=\"invoiceOfficalNo\" width=\"120\">\r\n            <template #default=\"scope\">\r\n              {{ scope.row.invoiceStatus }} {{ scope.row.invoiceOfficalNo }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"所属公司\" prop=\"invoiceBelongsTo\" width=\"50\"/>\r\n          <el-table-column align=\"center\" label=\"进销项\" prop=\"saleBuy\" width=\"50\">\r\n            <template #default=\"scope\">\r\n              {{ scope.row.saleBuy == \"sale\" ? \"+\" : \"-\" }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"发票性质\" prop=\"taxClass\" show-overflow-tooltip width=\"60\"/>\r\n          <el-table-column align=\"center\" label=\"发票类型\" prop=\"invoiceType\" show-overflow-tooltip width=\"60\"/>\r\n          <el-table-column align=\"center\" label=\"对方发票抬头\" prop=\"cooperatorCompanyTitle\" show-overflow-tooltip\r\n                           width=\"80\"\r\n          />\r\n          <el-table-column align=\"center\" label=\"结算金额\" prop=\"settlementAmount\" show-overflow-tooltip width=\"80\"/>\r\n          <el-table-column align=\"center\" label=\"发票币种\" prop=\"invoiceCurrencyCode\" width=\"60\"/>\r\n          <el-table-column align=\"center\" label=\"发票汇率\" prop=\"invoiceExchangeRate\" width=\"60\"/>\r\n          <el-table-column align=\"right\" label=\"发票金额\" prop=\"invoiceAmount\" width=\"180\">\r\n            <template #default=\"scope\">\r\n              {{\r\n                currency(scope.row.invoiceNetAmount).format({symbol: \"\", decimal: \".\", precision: 2})\r\n              }}+{{\r\n                currency(scope.row.vatAmount).format({symbol: \"\", decimal: \".\", precision: 2})\r\n              }}={{ currency(scope.row.invoiceVatAmount).format({symbol: \"\", decimal: \".\", precision: 2}) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"开票要求\" prop=\"opRemark\" show-overflow-tooltip width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"期望支付\" prop=\"expectedPayDate\" show-overflow-tooltip width=\"60\">\r\n            <template #default=\"scope\">\r\n              <span>{{ scope.row.expectedPayDate ? moment(scope.row.expectedPayDate).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"批复支付\" prop=\"approvedPayDate\" show-overflow-tooltip width=\"60\">\r\n            <template #default=\"scope\">\r\n              <span>{{ scope.row.approvedPayDate ? moment(scope.row.approvedPayDate).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"实际支付\" prop=\"actualPayDate\" show-overflow-tooltip width=\"60\">\r\n            <template #default=\"scope\">\r\n              <span>{{ scope.row.actualPayDate ? moment(scope.row.actualPayDate).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"计税月份\" prop=\"belongsToMonth\" show-overflow-tooltip width=\"80\"/>\r\n          <el-table-column align=\"center\" label=\"申请人\" prop=\"applyStuffId\" show-overflow-tooltip>\r\n            <template #default=\"scope\">\r\n              <span>{{\r\n                  getName(scope.row.applyStuffId)\r\n                }}-{{ scope.row.appliedTime ? moment(scope.row.appliedTime).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"审核人\" prop=\"auditStuffId\" show-overflow-tooltip>\r\n            <template #default=\"scope\">\r\n              <span>{{\r\n                  getName(scope.row.auditStuffId)\r\n                }}-{{ scope.row.auditTime ? moment(scope.row.auditTime).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"开票人\" prop=\"issuedStuffId\" show-overflow-tooltip>\r\n            <template #default=\"scope\">\r\n              <span>{{\r\n                  getName(scope.row.issuedStuffId)\r\n                }}-{{ scope.row.issuedTime ? moment(scope.row.issuedTime).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"报税人\" prop=\"taxStuffId\" show-overflow-tooltip>\r\n            <template #default=\"scope\">\r\n              <span>{{\r\n                  getName(scope.row.taxStuffId)\r\n                }}-{{ scope.row.taxDeclareTime ? moment(scope.row.taxDeclareTime).format(\"YYMMDD\") : \"\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改发票登记对话框 -->\r\n    <vatinvoice-dialog\r\n      :form=\"form\"\r\n      :invoice-items=\"invoiceItemList\"\r\n      :bank-account-list=\"bankAccountList\"\r\n      :company-list=\"companyList\"\r\n      :rules=\"rules\"\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      @cancel=\"cancel\"\r\n      @submit=\"handleDialogSubmit\"\r\n      @auditInvoice=\"handleAuditInvoice\"\r\n      @sendInvoice=\"handleSendInvoice\"\r\n      @invoiceApplyCancel=\"handleInvoiceApplyCancel\"\r\n      @invoiceAuditCancel=\"handleInvoiceAuditCancel\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addVatinvoice,\r\n  changeStatus,\r\n  delVatinvoice,\r\n  getVatinvoice,\r\n  listVatinvoice,\r\n  updateVatinvoice,\r\n  generateInvoiceExcel,\r\n  mergeInvoices, generateInvoiceCode\r\n} from \"@/api/system/vatInvoice\"\r\nimport store from \"@/store\"\r\nimport VatinvoiceDialog from \"./components/VatinvoiceDialog\"\r\nimport {updateDebitNoteByInvoiceId} from \"@/api/system/debitnote\"\r\nimport {parseTime} from \"../../../utils/rich\"\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\n\r\nexport default {\r\n  name: \"Vatinvoice\",\r\n  computed: {\r\n    moment() {\r\n      return moment\r\n    }\r\n  },\r\n  components: {\r\n    VatinvoiceDialog\r\n  },\r\n  data() {\r\n    return {\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 发票登记表格数据\r\n      vatinvoiceList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        invoiceCodeNo: null,\r\n        invoiceOfficalNo: null,\r\n        saleBuy: null,\r\n        invoiceBelongsTo: null,\r\n        richBankCode: null,\r\n        richCompanyTitle: null,\r\n        richVatSerialNo: null,\r\n        richBankAccount: null,\r\n        richBankFullname: null,\r\n        cooperatorId: null,\r\n        cooperatorShortName: null,\r\n        cooperatorBankCode: null,\r\n        cooperatorFullname: null,\r\n        cooperatorVatSerialNo: null,\r\n        cooperatorBankAccount: null,\r\n        cooperatorBankFullname: null,\r\n        rctNoSummary: null,\r\n        cooperatorReferNo: null,\r\n        officalChargeNameSummary: null,\r\n        chargeCurrencyCode: null,\r\n        dnSum: null,\r\n        dnRecieved: null,\r\n        dnBalance: null,\r\n        cnSum: null,\r\n        cnPaid: null,\r\n        cnBalance: null,\r\n        chargeClearStatus: null,\r\n        expectedPayDate: null,\r\n        approvedPayDate: null,\r\n        actualPayDate: null,\r\n        sqdBankStatementList: null,\r\n        invoiceCurrencyCode: null,\r\n        invoiceExchangeRate: null,\r\n        invoiceNetAmount: null,\r\n        vatAmount: null,\r\n        invoiceVatAmount: null,\r\n        saleNetSum: null,\r\n        saleTax: null,\r\n        saleTaxTotal: null,\r\n        buyNetSum: null,\r\n        buyTax: null,\r\n        buyTaxTotal: null,\r\n        taxClass: null,\r\n        invoiceType: null,\r\n        belongsToMonth: null,\r\n        invoiceStatus: null,\r\n        invoiceRemark: null,\r\n        applyStuffId: null,\r\n        appliedTime: null,\r\n        issuedStuffId: null,\r\n        issuedTime: null,\r\n        taxStuffId: null,\r\n        taxDeclareTime: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        invoiceCodeNo: [\r\n          {\r\n            required: true,\r\n            message: \"发票流水号不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        saleBuy: [\r\n          {\r\n            required: true,\r\n            message: \"进销标志：sale=销项，buy=进项不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      },\r\n      invoiceItemList: [],\r\n      companyList: [],\r\n      bankAccountList: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadStaff()\r\n  },\r\n  methods: {\r\n    selectableRow(row) {\r\n      return row.auditStuffId !== null\r\n    },\r\n    async handleInvoiceAuditCancel(formData) {\r\n      // 修改发票\r\n      const response = await updateVatinvoice(formData)\r\n      // 更新分账单\r\n      const updateData = {\r\n        invoiceId: formData.invoiceId,\r\n        invoiceStatus: \"unissued\"\r\n      }\r\n      const updateResponse = await updateDebitNoteByInvoiceId(updateData)\r\n      if (updateResponse.code === 200 && response.code === 200) {\r\n        this.$message.success(\"取消审核成功\")\r\n        this.getList()\r\n        this.open = false\r\n      } else {\r\n        this.$message.error(\"取消审核失败\")\r\n      }\r\n    },\r\n    async handleInvoiceApplyCancel(formData) {\r\n      // 修改发票\r\n      const response = await updateVatinvoice(formData)\r\n      // 更新分账单\r\n      const updateData = {\r\n        invoiceId: formData.invoiceId,\r\n        invoiceStatus: \"unissued\"\r\n      }\r\n      const updateResponse = await updateDebitNoteByInvoiceId(updateData)\r\n      if (updateResponse.code === 200 && response.code === 200) {\r\n        this.$message.success(\"取消申请成功\")\r\n        this.getList()\r\n        this.open = false\r\n      } else {\r\n        this.$message.error(\"取消申请失败\")\r\n      }\r\n    },\r\n    currency,\r\n    parseTime,\r\n    handleRowClick(row) {\r\n      this.handleUpdate(row)\r\n    },\r\n    /** 查询发票登记列表 */\r\n    getList() {\r\n      this.loading = true\r\n      // 不显示未申请开票的发票\r\n      listVatinvoice(this.queryParams).then(response => {\r\n        this.vatinvoiceList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        invoiceId: null,\r\n        invoiceCodeNo: null,\r\n        invoiceOfficalNo: null,\r\n        saleBuy: null,\r\n        invoiceBelongsTo: null,\r\n        richBankCode: null,\r\n        richCompanyTitle: null,\r\n        richVatSerialNo: null,\r\n        richBankAccount: null,\r\n        richBankFullname: null,\r\n        cooperatorId: null,\r\n        cooperatorShortName: null,\r\n        cooperatorBankCode: null,\r\n        cooperatorFullname: null,\r\n        cooperatorVatSerialNo: null,\r\n        cooperatorBankAccount: null,\r\n        cooperatorBankFullname: null,\r\n        rctNoSummary: null,\r\n        cooperatorReferNo: null,\r\n        officalChargeNameSummary: null,\r\n        chargeCurrencyCode: null,\r\n        dnSum: null,\r\n        dnRecieved: null,\r\n        dnBalance: null,\r\n        cnSum: null,\r\n        cnPaid: null,\r\n        cnBalance: null,\r\n        chargeClearStatus: \"0\",\r\n        expectedPayDate: null,\r\n        approvedPayDate: null,\r\n        actualPayDate: null,\r\n        sqdBankStatementList: null,\r\n        invoiceCurrencyCode: null,\r\n        invoiceExchangeRate: null,\r\n        invoiceNetAmount: null,\r\n        vatAmount: null,\r\n        invoiceVatAmount: null,\r\n        saleNetSum: null,\r\n        saleTax: null,\r\n        saleTaxTotal: null,\r\n        buyNetSum: null,\r\n        buyTax: null,\r\n        buyTaxTotal: null,\r\n        taxClass: null,\r\n        invoiceType: null,\r\n        belongsToMonth: null,\r\n        invoiceStatus: \"0\",\r\n        invoiceRemark: null,\r\n        applyStuffId: null,\r\n        appliedTime: null,\r\n        issuedStuffId: null,\r\n        issuedTime: null,\r\n        taxStuffId: null,\r\n        taxDeclareTime: null,\r\n        createTime: null,\r\n        updateTime: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.invoiceId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 判断行是否可选\r\n    isSelectable(row) {\r\n      // 发票状态为merged的行不可勾选\r\n      return row.invoiceStatus !== \"merged\" && row.mergeInvoice === \"1\"\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.invoiceId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加发票登记\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const invoiceId = row.invoiceId || this.ids\r\n      getVatinvoice(invoiceId).then(response => {\r\n        this.form = response.data\r\n        this.companyList = response.extCompanyList\r\n        this.bankAccountList = response.basAccountList\r\n        this.open = true\r\n        this.title = \"修改发票登记\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 已移至handleDialogSubmit方法\r\n    },\r\n\r\n    /** 处理对话框提交 */\r\n    handleDialogSubmit(formData) {\r\n      if (formData.invoiceId != null) {\r\n        updateVatinvoice(formData).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n          this.open = false\r\n          this.getList()\r\n        })\r\n      } else {\r\n        addVatinvoice(formData).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\")\r\n          this.open = false\r\n          this.getList()\r\n        })\r\n      }\r\n    },\r\n    /** 处理审核发票事件 */\r\n    async handleAuditInvoice(row) {\r\n      let invoiceCodeNo\r\n      if (row.rctId && row.cooperatorId) {\r\n        invoiceCodeNo = await this.generateInvoiceCodeNo(row.rctId, row.cooperatorId)\r\n      }\r\n      row.invoiceCodeNo = invoiceCodeNo\r\n      row.rsChargeList = row.rsChargeList.map(charge => {\r\n        return {\r\n          ...charge,\r\n          sqdInvoiceCodeNo: invoiceCodeNo\r\n        }\r\n      })\r\n      // 调用API更新发票审核信息\r\n      const response = await updateVatinvoice(row)\r\n\r\n      // 更新账单发票信息->已审核\r\n      const response2 = await updateDebitNoteByInvoiceId({\r\n        invoiceId: row.invoiceId,\r\n        invoiceCodeNo: invoiceCodeNo,\r\n        invoiceStatus: \"confirmed\"\r\n      })\r\n\r\n      if (response.code === 200 && response2.code === 200) {\r\n        this.$message.success(\"信息审核成功\")\r\n        this.getList()\r\n      }\r\n    },\r\n    // 生成发票流水号\r\n    async generateInvoiceCodeNo(rctId, cooperatorId) {\r\n      try {\r\n        // 调用API生成发票编码\r\n        const response = await generateInvoiceCode(rctId, cooperatorId)\r\n        if (response.code === 200) {\r\n          return response.msg\r\n        }\r\n      } catch (error) {\r\n        console.error(\"生成发票编码失败:\", error)\r\n      }\r\n      // 如果API调用失败，返回默认格式\r\n      return \"\"\r\n    },\r\n    /** 处理发送开票事件 */\r\n    handleSendInvoice(formData) {\r\n      // 更新debitNote为已开票\r\n      const invoiceId = formData.invoiceId\r\n      if (invoiceId) {\r\n        updateDebitNoteByInvoiceId({invoiceId, invoiceStatus: \"issued\"})\r\n          .then(response => {\r\n            if (response.code === 200) {\r\n              this.$message.success(\"分账单发票状态更新成功\")\r\n            } else {\r\n              this.$message.error(response.msg || \"分账单发票状态更新失败\")\r\n            }\r\n          })\r\n          .catch(error => {\r\n            console.error(\"分账单发票状态更新失败:\", error)\r\n            this.$message.error(\"分账单发票状态更新失败，请重试\")\r\n          })\r\n      }\r\n\r\n      // 刷新列表\r\n      this.getList()\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const invoiceIds = row.invoiceId || this.ids\r\n      this.$modal.confirm(\"是否确认删除发票登记编号为\\\"\" + invoiceIds + \"\\\"的数据项？\").then(function () {\r\n        return delVatinvoice(invoiceIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出开票资料按钮操作 */\r\n    handleExportTheInvoicingInformation() {\r\n      // 检查是否有选中的发票\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请先选择要导出的发票\")\r\n        return\r\n      }\r\n\r\n      generateInvoiceExcel(this.ids)\r\n        .then(response => {\r\n          // 获取文件的字节数组 (ArrayBuffer)\r\n          const data = response\r\n\r\n          // 生成文件名\r\n          let fileName = `Invoices_${new Date().getTime()}.xlsx`\r\n\r\n          // 创建一个 Blob 对象来存储文件\r\n          const blob = new Blob([data], {\r\n            type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"  // Excel 文件类型\r\n          })\r\n\r\n          // 创建一个临时链接，模拟点击来下载文件\r\n          const link = document.createElement(\"a\")\r\n          const url = window.URL.createObjectURL(blob)  // 创建一个 URL 指向 Blob 对象\r\n          link.href = url\r\n          link.download = fileName  // 设置下载的文件名\r\n\r\n          // 模拟点击链接，触发下载\r\n          document.body.appendChild(link)\r\n          link.click()\r\n\r\n          // 下载完成后移除链接，并释放 URL 对象\r\n          document.body.removeChild(link)\r\n          window.URL.revokeObjectURL(url)\r\n\r\n          this.$modal.msgSuccess(\"Excel文件导出成功\")\r\n        })\r\n        .catch(error => {\r\n          console.error(\"文件下载失败:\", error)\r\n          this.$modal.msgError(\"Excel文件导出失败\")\r\n        })\r\n    },\r\n    /** 合并发票按钮操作 */\r\n    handleMergeInvoices() {\r\n\r\n      if (this.isSelectable) {\r\n        this.$modal.msgError(\"选中的发票有不能合并的项目\")\r\n        return\r\n      }\r\n\r\n      // 检查是否有选中的发票\r\n      if (this.ids.length < 2) {\r\n        this.$modal.msgError(\"请至少选择2张发票进行合并\")\r\n        return\r\n      }\r\n\r\n      // 获取选中的发票数据\r\n      const selectedInvoices = this.vatinvoiceList.filter(item => this.ids.includes(item.invoiceId))\r\n\r\n      // 检查发票状态和类型是否一致\r\n      const firstInvoice = selectedInvoices[0]\r\n      const canMerge = selectedInvoices.every(invoice => {\r\n        // 检查发票状态 - 只能合并未开票的发票\r\n        if (invoice.invoiceStatus !== \"unissued\" && invoice.invoiceStatus !== \"applied\") {\r\n          return false\r\n        }\r\n        // 检查进销项是否一致\r\n        if (invoice.saleBuy !== firstInvoice.saleBuy) {\r\n          return false\r\n        }\r\n        // 检查对方公司是否一致\r\n        if (invoice.cooperatorId !== firstInvoice.cooperatorId) {\r\n          return false\r\n        }\r\n        // 检查币种是否一致\r\n        if (invoice.invoiceCurrencyCode !== firstInvoice.invoiceCurrencyCode) {\r\n          return false\r\n        }\r\n        return true\r\n      })\r\n\r\n      if (!canMerge) {\r\n        this.$modal.msgError(\"只能合并状态为未开票或已申请、进销项一致、对方公司一致、币种一致的发票\")\r\n        return\r\n      }\r\n\r\n      // 确认合并操作\r\n      this.$modal.confirm(`确认要合并选中的 ${this.ids.length} 张发票吗？合并后将生成一张新的发票，原发票将被标记为已合并。`).then(() => {\r\n        mergeInvoices(this.ids).then(response => {\r\n          if (response.code === 200) {\r\n            this.$modal.msgSuccess(\"发票合并成功\")\r\n            this.getList() // 刷新列表\r\n          } else {\r\n            this.$modal.msgError(response.msg || \"发票合并失败\")\r\n          }\r\n        }).catch(error => {\r\n          console.error(\"发票合并失败:\", error)\r\n          this.$modal.msgError(\"发票合并失败，请重试\")\r\n        })\r\n      }).catch(() => {\r\n        // 用户取消操作\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/vatinvoice/export\", {\r\n        ...this.queryParams\r\n      }, `vatinvoice_${new Date().getTime()}.xlsx`)\r\n    },\r\n    getName(id) {\r\n      if (id) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff) {\r\n          return staff.staffShortName\r\n        } else {\r\n          return \"\"\r\n        }\r\n      } else {\r\n        return \"\"\r\n      }\r\n    },\r\n    loadStaff() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === \"业务部\")\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === \"业务部\")\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}