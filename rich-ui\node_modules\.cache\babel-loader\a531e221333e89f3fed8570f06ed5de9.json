{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\components\\VatinvoiceDialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\components\\VatinvoiceDialog.vue", "mtime": 1756454512254}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_company", "require", "_account", "_vatInvoice", "_FileUpload", "_interopRequireDefault", "_vueTreeselect", "_store", "_debitnote", "debounce", "fn", "delay", "timer", "context", "args", "arguments", "clearTimeout", "setTimeout", "apply", "_default2", "name", "components", "FileUpload", "Treeselect", "props", "visible", "type", "Boolean", "default", "title", "String", "form", "Object", "_default", "rules", "invoiceItems", "Array", "companyList", "bankAccountList", "data", "dialogVisible", "formData", "invoiceItemList", "debouncedFetchCompanyInfo", "companyBankList", "invoicingItemOptions", "id", "invoicingItemName", "children", "selectedRows", "batchInvoicingItemDialogVisible", "batchForm", "invoicingItem", "computed", "availableBankList", "length", "created", "fetchCompanyInfo", "watch", "handler", "val", "_this", "JSON", "parse", "stringify", "invoiceAttachment", "invoiceBelongsTo", "autoFillCompanyInfo", "$nextTick", "calculateInvoiceAmount", "immediate", "newVal", "_this2", "keys", "deep", "oldVal", "beforeMount", "loadStaff", "methods", "handleInvoiceAuditCancel", "auditStuffId", "auditTime", "auditStatus", "invoiceCodeNo", "rsChargeList", "for<PERSON>ach", "charge", "$emit", "handleInvoiceApplyCancel", "applyStuffId", "appliedTime", "invoiceStatus", "handleSelectionChange", "selection", "showBatchInvoicingItemDialog", "confirmBatchSetInvoicingItem", "_this3", "$message", "warning", "row", "success", "concat", "batchSetDefaultInvoicingItem", "chargeName", "includes", "batchClearInvoicingItem", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "customClass", "then", "catch", "invoicingItemNormalizer", "node", "_this5", "normalized", "label", "map", "child", "isArray", "invoiceNetAmount", "vatAmount", "reduce", "acc", "dutyRate", "parseFloat", "dnAmount", "dnUnitRate", "netAmount", "subtotal", "saleBuy", "dnSum", "cnSum", "invoiceCurrencyCode", "invoiceExchangeRate", "exchangeRate", "taxAmount", "invoiceType", "toFixed", "invoiceAmount", "formatCurrency", "settlementAmount", "getInvoiceAmountPlaceholder", "amount", "num", "isNaN", "submitForm", "_this6", "$refs", "validate", "valid", "collectRctNumbers", "handleCancel", "handleClose", "searchAvailableInvoice", "console", "log", "fetchCompanyBankAccounts", "_this7", "cooperatorId", "listAccount", "belongToCompany", "response", "code", "rows", "info", "error", "handleBankAccountChange", "bankCode", "selectedAccount", "find", "item", "cooperatorBankFullname", "bankBranchName", "cooperator<PERSON><PERSON>k<PERSON><PERSON>unt", "bankAccount", "companyId", "_this8", "getCompany", "companyInfo", "cooperatorCompanyTitle", "companyLocalName", "cooperatorVatSerialNo", "taxNo", "cooperatorShortName", "companyShortName", "companyCode", "companyInfoMap", "richCompanyTitle", "richVatSerialNo", "isDisabled", "taxLocked", "handleRichBankCodeChange", "richBankFullname", "rich<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "getInvoiceStatusType", "status", "statusMap", "getInvoiceStatusText", "handleApplyInvoice", "_this9", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "currentUser", "currentTime", "updateData", "wrap", "_callee$", "_context", "prev", "next", "$store", "state", "user", "Date", "toISOString", "_objectSpread2", "sid", "updateVatinvoice", "sent", "msg", "t0", "stop", "handleAuditInvoice", "_this10", "_callee2", "_callee2$", "_context2", "handleSendInvoice", "_this11", "_callee3", "_callee3$", "_context3", "abrupt", "issuedStuffId", "issuedTime", "canApplyInvoice", "invoiceId", "getApplyInfo", "applyTime", "toLocaleString", "year", "month", "day", "hour", "minute", "e", "userName", "getName", "actualPayDate", "staff", "allRsStaffList", "filter", "rsStaff", "staffId", "staffFamilyLocalName", "staffGivingLocalName", "staffShortName", "_this12", "redisList", "store", "dispatch", "staffList", "dept", "deptLocalName", "rctNumbers", "Set", "sqdRctNo", "trim", "add", "rctArray", "from", "sort", "relatedOrderNo", "join", "exports"], "sources": ["src/views/system/vatinvoice/components/VatinvoiceDialog.vue"], "sourcesContent": ["<template>\r\n  <el-dialog\r\n    v-dialogDrag\r\n    v-dialogDragWidth\r\n    :close-on-click-modal=\"false\"\r\n    :modal-append-to-body=\"false\"\r\n    :title=\"title\"\r\n    :visible.sync=\"dialogVisible\"\r\n    append-to-body\r\n    modal\r\n    width=\"80%\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <el-form ref=\"form\" :model=\"formData\" :rules=\"rules\" class=\"edit\" label-width=\"80px\" size=\"mini\">\r\n      <!-- 第一行 - 基本信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票流水号\">\r\n            <el-input v-model=\"formData.invoiceCodeNo\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                      placeholder=\"发票流水号\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"进销标志\">\r\n            <el-select v-model=\"formData.saleBuy\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"进销标志\"\r\n                       style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"销项\" value=\"sale\"/>\r\n              <el-option label=\"进项\" value=\"buy\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票性质\">\r\n            <!-- 主营业务收入/非主营收入/营业外收入/成本/费用 -->\r\n            <el-select v-model=\"formData.taxClass\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"发票性质\"\r\n                       style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"主营业务收入\" value=\"主营业务收入\"/>\r\n              <el-option label=\"非主营业务收入\" value=\"非主营业务收入\"/>\r\n              <el-option label=\"营业外收入\" value=\"营业外收入\"/>\r\n              <el-option label=\"成本\" value=\"成本\"/>\r\n              <el-option label=\"费用\" value=\"费用\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票类型\">\r\n            <el-select v-model=\"formData.invoiceType\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"发票类型\" style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"增值税专用发票\" value=\"增值税专用发票\"/>\r\n              <el-option label=\"普通发票\" value=\"普通发票\"/>\r\n              <el-option label=\"收据\" value=\"收据\"/>\r\n              <el-option label=\"无票收支\" value=\"无票收支\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"接收合开\">\r\n            <el-row>\r\n              <el-col :offset=\"1\" :span=\"8\">\r\n                <el-checkbox v-model=\"formData.mergeInvoice\" :class=\"{'disable-form': isDisabled()}\"\r\n                             :disabled=\"isDisabled()\" false-label=\"0\"\r\n                             true-label=\"1\"\r\n                >\r\n                </el-checkbox>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-tag :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                        :type=\"getInvoiceStatusType(formData.invoiceStatus)\"\r\n                >{{ getInvoiceStatusText(formData.invoiceStatus) }}\r\n                </el-tag>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票号码\">\r\n            <el-row>\r\n              <el-col :span=\"18\">\r\n                <el-input v-model=\"formData.invoiceOfficalNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"发票号码\"\r\n                />\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-popover\r\n                  placement=\"top-start\"\r\n                  title=\"发票附件管理\"\r\n                  trigger=\"hover\"\r\n                  width=\"300\"\r\n                >\r\n                  <div>\r\n                    <file-upload\r\n                      :class=\"isDisabled()?'disable-form':''\"\r\n                      :file-type=\"['pdf']\"\r\n                      :is-tip-flex=\"true\"\r\n                      :value=\"formData.invoiceAttachment\"\r\n                      @input=\"formData.invoiceAttachment=$event\"\r\n                    />\r\n                  </div>\r\n                  <template #reference>\r\n                    <el-button size=\"mini\" type=\"text\">\r\n                      [发票]\r\n                    </el-button>\r\n                  </template>\r\n                </el-popover>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二行 - 公司和账户信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"16\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"所属公司\">\r\n                <el-input v-model=\"formData.invoiceBelongsTo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"所属公司\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"我司账户\">\r\n                <tree-select :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"formData.richBankCode\" :placeholder=\"'银行账户'\"\r\n                             :type=\"'companyAccount'\" @return=\"formData.richBankCode=$event\"\r\n                             :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                             @returnData=\"handleRichBankCodeChange\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"对方公司\">\r\n                <company-select :load-options=\"companyList\"\r\n                                :multiple=\"false\" :no-parent=\"true\"\r\n                                :pass=\"formData.cooperatorId\" :placeholder=\"''\"\r\n                                @return=\"formData.cooperatorId=$event\"\r\n                                :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"对方账户\">\r\n                <el-select v-model=\"formData.cooperatorBankCode\" :class=\"{'disable-form': isDisabled()}\"\r\n                           :disabled=\"isDisabled()\"\r\n                           placeholder=\"对方账户\" style=\"width: 100%\"\r\n                           @change=\"handleBankAccountChange\" @click.native=\"fetchCompanyBankAccounts\"\r\n                >\r\n                  <el-option v-for=\"item in availableBankList\" :key=\"item.bankAccId\"\r\n                             :label=\"item.bankAccCode+ '('+item.bankAccount+')'\" :value=\"item.bankAccCode\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发票抬头\">\r\n                <el-input v-model=\"formData.richCompanyTitle\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司发票抬头\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发票抬头\">\r\n                <el-input v-model=\"formData.cooperatorCompanyTitle\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方发票抬头\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n\r\n        <el-col :span=\"8\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"formData.opRemark\" :minrows=\"3\" :rows=\"2\"\r\n                        placeholder=\"开票要求\" type=\"textarea\"\r\n                        :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n              />\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"formData.relatedOrderNo\" :minrows=\"3\" :rows=\"2\" placeholder=\"相关订单号\"\r\n                        type=\"textarea\"\r\n                        :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n              />\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第四行 - 税号信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"16\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"税号\">\r\n                <el-input v-model=\"formData.richVatSerialNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司纳税人识别号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"税号\">\r\n                <el-input v-model=\"formData.cooperatorVatSerialNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\" placeholder=\"对方纳税人识别号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行全称\">\r\n                <el-input v-model=\"formData.richBankFullname\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司银行全称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行全称\">\r\n                <el-input v-model=\"formData.cooperatorBankFullname\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方银行全称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行账号\">\r\n                <el-input v-model=\"formData.richBankAccount\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司账号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行账号\">\r\n                <el-input v-model=\"formData.cooperatorBankAccount\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方账号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"结算币种\">\r\n                <tree-select :class=\"{'disable-form': isDisabled()}\"\r\n                             :disabled=\"isDisabled()\" :pass=\"formData.settlementCurrencyCode\"\r\n                             :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                             style=\"width: 100%\" @return=\"formData.settlementCurrencyCode=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"结算金额\">\r\n                <el-input v-model=\"formData.settlementAmount\" class=\"disable-form\"\r\n                          disabled placeholder=\"结算金额\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"发票汇率\">\r\n                <el-input v-model=\"formData.invoiceExchangeRate\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"发票汇率\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"发票金额\">\r\n                <el-input v-model=\"formData.invoiceAmount\" :placeholder=\"getInvoiceAmountPlaceholder()\"\r\n                          class=\"disable-form\"\r\n                          disabled\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n\r\n        <el-col :span=\"4\">\r\n          <el-input v-model=\"formData.invoiceRemark\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                    :minrows=\"6\" :rows=\"6\"\r\n                    placeholder=\"税务发票备注栏\" type=\"textarea\"\r\n          />\r\n        </el-col>\r\n\r\n        <el-col :span=\"4\">\r\n          <el-col>\r\n            <el-form-item label=\"期望支付日\">\r\n              <el-date-picker v-model=\"formData.expectedPayDate\"\r\n                              clearable\r\n                              placeholder=\"期望支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"批复支付日\">\r\n              <el-date-picker v-model=\"formData.approvedPayDate\"\r\n                              clearable\r\n                              placeholder=\"批复支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"实际支付日\">\r\n              <el-date-picker v-model=\"formData.actualPayDate\"\r\n                              clearable\r\n                              placeholder=\"实际支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"报税月份\">\r\n              <el-input v-model=\"formData.belongsToMonth\"\r\n                        :class=\"{'disable-form': isDisabled() || type === 'debitNote'}\"\r\n                        :disabled=\"isDisabled() || type === 'debitNote'\"\r\n                        class=\"yellow-bg\" placeholder=\"2025/7/31\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-col>\r\n      </el-row>\r\n\r\n\r\n      <el-divider></el-divider>\r\n\r\n      <!-- 发票明细表格 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"24\">\r\n          <!-- 批量操作工具栏 -->\r\n          <div style=\"margin-bottom: 10px; display: flex; align-items: center; gap: 10px;\">\r\n            <el-button\r\n              :disabled=\"isDisabled() || !selectedRows.length\"\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"showBatchInvoicingItemDialog\"\r\n            >\r\n              批量设置开票项目\r\n            </el-button>\r\n            <el-button\r\n              :disabled=\"isDisabled() || !selectedRows.length\"\r\n              icon=\"el-icon-check\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"batchSetDefaultInvoicingItem\"\r\n            >\r\n              批量设置默认项目\r\n            </el-button>\r\n            <el-button\r\n              :disabled=\"isDisabled() || !selectedRows.length\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"batchClearInvoicingItem\"\r\n            >\r\n              批量清空开票项目\r\n            </el-button>\r\n            <span style=\"margin-left: 20px; color: #606266;\">\r\n              已选择 {{ selectedRows.length }} 项\r\n            </span>\r\n          </div>\r\n\r\n          <el-table\r\n            ref=\"chargeTable\"\r\n            :data=\"formData.rsChargeList\"\r\n            border\r\n            size=\"mini\"\r\n            style=\"width: 100%\"\r\n            @selection-change=\"handleSelectionChange\"\r\n          >\r\n            <el-table-column align=\"center\" type=\"selection\" width=\"35\"/>\r\n            <el-table-column align=\"center\" label=\"账单编号\" prop=\"sqdInvoiceCodeNo\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"RCT号\" prop=\"sqdRctNo\"/>\r\n            <el-table-column align=\"center\" label=\"所属服务\" prop=\"serviceLocalName\" width=\"100\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.sqdServiceTypeId == 0 ? \"客户应收\" : scope.row.serviceLocalName }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"费用名称\" prop=\"chargeName\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"备注\" prop=\"chargeRemark\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"收付标志\" prop=\"isReceivingOrPaying\" width=\"80\">\r\n              <template #default=\"scope\">{{ scope.row.isReceivingOrPaying == 0 ? \"应收\" : \"应付\" }}</template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"报价币种\" prop=\"quoteCurrency\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"单价\" prop=\"dnUnitRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"数量\" prop=\"dnAmount\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"单位\" prop=\"dnUnitCode\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"结算汇率\" prop=\"basicCurrencyRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"结算币种\" prop=\"dnCurrencyCode\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"税率\" prop=\"dutyRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"税金\" prop=\"dutyRate\" width=\"80\">\r\n              <template #default=\"scope\">\r\n                {{ (scope.row.dutyRate / 100) * scope.row.dnAmount * scope.row.dnUnitRate }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"含税小计\" prop=\"subtotal\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"开票项目名称\" prop=\"invoicingItem\" width=\"150\">\r\n              <template #default=\"scope\">\r\n                <treeselect v-model=\"scope.row.invoicingItem\"\r\n                            :class=\"{'disable-form': isDisabled()}\"\r\n                            :default-expand-level=\"1\"\r\n                            :disable-branch-nodes=\"true\"\r\n                            :disabled=\"isDisabled()\"\r\n                            :normalizer=\"invoicingItemNormalizer\"\r\n                            :options=\"invoicingItemOptions\"\r\n                            :show-count=\"true\"\r\n                            :z-index=\"9999\"\r\n                            append-to-body\r\n                            placeholder=\"开票项目名称\"\r\n                            searchable\r\n                />\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"税收编码\" prop=\"taxCode\" width=\"100\"/>\r\n          </el-table>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 批量设置开票项目对话框 -->\r\n      <el-dialog\r\n        :visible.sync=\"batchInvoicingItemDialogVisible\"\r\n        append-to-body\r\n        title=\"批量设置开票项目\"\r\n        width=\"500px\"\r\n      >\r\n        <el-form :model=\"batchForm\" label-width=\"120px\">\r\n          <el-form-item label=\"开票项目名称\">\r\n            <treeselect\r\n              v-model=\"batchForm.invoicingItem\"\r\n              :default-expand-level=\"1\"\r\n              :disable-branch-nodes=\"true\"\r\n              :normalizer=\"invoicingItemNormalizer\"\r\n              :options=\"invoicingItemOptions\"\r\n              :show-count=\"true\"\r\n              placeholder=\"请选择开票项目名称\"\r\n              searchable\r\n            />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button @click=\"batchInvoicingItemDialogVisible = false\">取 消</el-button>\r\n          <el-button type=\"primary\" @click=\"confirmBatchSetInvoicingItem\">确 定</el-button>\r\n        </div>\r\n      </el-dialog>\r\n\r\n      <!-- 操作按钮组 -->\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n        <el-col :span=\"3\">\r\n          <el-button icon=\"el-icon-check\" size=\"mini\" type=\"primary\">√默认对冲</el-button>\r\n          <div>已选总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">智选</el-button>\r\n          <div>未选总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">反选</el-button>\r\n          <div>全部总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button\r\n            :disabled=\"isDisabled()\"\r\n            size=\"mini\"\r\n            type=\"primary\" @click=\"submitForm\"\r\n          >\r\n            保存草稿\r\n          </el-button>\r\n          <div>{{ getName(formData.issuedStuffId) }}</div>\r\n          <div>{{ formData.issuedTime }}</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <div>\r\n            <el-button v-if=\"formData.applyStuffId === null\" size=\"mini\"\r\n                       type=\"primary\" @click=\"handleApplyInvoice\"\r\n            >{{ formData.saleBuy === \"sale\" ? \"申请开票\" : \"确认账单\" }}\r\n            </el-button>\r\n            <el-button v-show=\"formData.applyStuffId !== null\" :disabled=\"type === 'debitNote' || formData.auditStuffId !== null\" size=\"mini\"\r\n                       type=\"primary\" @click=\"handleInvoiceApplyCancel\"\r\n            >{{ formData.saleBuy === \"sale\" ? \"取消开票\" : \"取消账单\" }}\r\n            </el-button>\r\n          </div>\r\n\r\n          <div>{{ getName(formData.applyStuffId) }}</div>\r\n          <div>{{ formData.appliedTime }}</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <div>\r\n            <el-button\r\n              v-if=\"formData.auditStuffId === null\"\r\n              :disabled=\"type === 'debitNote' || formData.auditStuffId !== null\"\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAuditInvoice\"\r\n            >\r\n              财务审核\r\n            </el-button>\r\n            <el-button\r\n              v-else\r\n              :disabled=\"type === 'debitNote' || formData.auditStuffId === null\"\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleInvoiceAuditCancel\"\r\n            >\r\n              取消审核\r\n            </el-button>\r\n          </div>\r\n\r\n          <div>{{ getName(formData.auditStuffId) }}</div>\r\n          <div>{{ formData.auditTime }}</div>\r\n        </el-col>\r\n        <!--<el-col :span=\"3\">\r\n          <el-button\r\n            :disabled=\"type === 'debitNote' || formData.auditStatus !== '审核通过'\"\r\n            size=\"mini\"\r\n            type=\"success\"\r\n            @click=\"handleSendInvoice\"\r\n          >\r\n            发送开票\r\n          </el-button>\r\n          <div>{{ getName(formData.issuedStuffId) }}</div>\r\n          <div>{{ formData.issuedTime }}</div>\r\n        </el-col>-->\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"warning\">打印</el-button>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"info\">报税锁定</el-button>\r\n          <div>报税人+时间</div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport {getCompany} from \"@/api/system/company\"\r\nimport {listAccount} from \"@/api/system/account\"\r\nimport {updateVatinvoice} from \"@/api/system/vatInvoice\"\r\nimport FileUpload from \"@/components/FileUpload\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\nimport store from \"@/store\"\r\nimport {updateDebitNoteByInvoiceId} from \"@/api/system/debitnote\"\r\n\r\n// 防抖函数\r\nfunction debounce(fn, delay) {\r\n  let timer = null\r\n  return function () {\r\n    const context = this\r\n    const args = arguments\r\n    clearTimeout(timer)\r\n    timer = setTimeout(function () {\r\n      fn.apply(context, args)\r\n    }, delay)\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: \"VatinvoiceDialog\",\r\n  components: {\r\n    FileUpload,\r\n    Treeselect\r\n  },\r\n  props: {\r\n    // 是否显示对话框\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 标题\r\n    title: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 表单验证规则\r\n    rules: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 发票明细列表\r\n    invoiceItems: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 银行账户列表\r\n    bankAccountList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    type: {\r\n      type: String,\r\n      default: \"\"\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 内部对话框可见性状态\r\n      dialogVisible: false,\r\n      // 表单数据的副本\r\n      formData: {},\r\n      // 发票明细列表的副本\r\n      invoiceItemList: [],\r\n      // 防抖后的获取公司信息方法\r\n      debouncedFetchCompanyInfo: null,\r\n      // 公司银行账户列表\r\n      companyBankList: [],\r\n      // 开票项目选项数据\r\n      invoicingItemOptions: [\r\n        {\r\n          id: \"1\",\r\n          invoicingItemName: \"经纪代理服务\",\r\n          children: [\r\n            {id: \"1-1\", invoicingItemName: \"代理运费\"},\r\n            {id: \"1-2\", invoicingItemName: \"国际货物运输代理服务费\"},\r\n            {id: \"1-3\", invoicingItemName: \"代理港杂费\"},\r\n            {id: \"1-4\", invoicingItemName: \"国际货物运输代理服务\"},\r\n            {id: \"1-5\", invoicingItemName: \"代理报关服务费\"},\r\n            {id: \"1-6\", invoicingItemName: \"代理服务费\"},\r\n            {id: \"1-7\", invoicingItemName: \"代理报关费\"},\r\n            {id: \"1-8\", invoicingItemName: \"代理拖车费\"},\r\n            {id: \"1-9\", invoicingItemName: \"国际货物运输代理服务-代理运费\"},\r\n            {id: \"1-10\", invoicingItemName: \"代理国内运费\"},\r\n            {id: \"1-11\", invoicingItemName: \"国际货物运输代理海运费\"},\r\n            {id: \"1-13\", invoicingItemName: \"运输代理费\"},\r\n            {id: \"1-14\", invoicingItemName: \"货物运输代理服务费\"},\r\n            {id: \"1-15\", invoicingItemName: \"国际货物运输代理港杂费\"},\r\n            {id: \"1-16\", invoicingItemName: \"国际货物运输代理运费\"},\r\n            {id: \"1-17\", invoicingItemName: \"货物运输代理费\"},\r\n            {id: \"1-18\", invoicingItemName: \"国际货物运输代理费\"},\r\n            {id: \"1-19\", invoicingItemName: \"代理杂费\"},\r\n            {id: \"1-20\", invoicingItemName: \"代理文件费\"},\r\n            {id: \"1-21\", invoicingItemName: \"代理设备交接单费用\"},\r\n            {id: \"1-22\", invoicingItemName: \"代理舱单申报费\"},\r\n            {id: \"1-23\", invoicingItemName: \"代理操作费\"},\r\n            {id: \"1-24\", invoicingItemName: \"代理封条费\"},\r\n            {id: \"1-25\", invoicingItemName: \"代理码头操作费\"},\r\n            {id: \"1-26\", invoicingItemName: \"代理电放费\"},\r\n            {id: \"1-27\", invoicingItemName: \"代理核重费\"}\r\n          ]\r\n        }\r\n      ],\r\n      // 批量选择相关数据\r\n      selectedRows: [], // 选中的行数据\r\n      batchInvoicingItemDialogVisible: false, // 批量设置开票项目对话框可见性\r\n      batchForm: {\r\n        invoicingItem: null // 批量设置的开票项目\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 可用的银行账户列表：优先使用companyBankList，为空时使用传入的bankAccountList\r\n    availableBankList() {\r\n      return this.companyBankList.length > 0 ? this.companyBankList : this.bankAccountList\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 创建防抖版本的fetchCompanyInfo方法，设置300ms延迟\r\n    this.debouncedFetchCompanyInfo = debounce(this.fetchCompanyInfo, 300)\r\n  },\r\n  watch: {\r\n    visible: {\r\n      handler(val) {\r\n        this.dialogVisible = val\r\n        if (val) {\r\n          // 当对话框显示时，复制传入的数据\r\n          this.formData = JSON.parse(JSON.stringify(this.form))\r\n          this.invoiceItemList = JSON.parse(JSON.stringify(this.invoiceItems))\r\n\r\n          // 确保发票附件字段存在\r\n          if (!this.formData.invoiceAttachment) {\r\n            this.formData.invoiceAttachment = \"\"\r\n          }\r\n\r\n          // 如果所属公司已有值，自动填充相关信息\r\n          if (this.formData.invoiceBelongsTo) {\r\n            this.autoFillCompanyInfo(this.formData.invoiceBelongsTo)\r\n          }\r\n\r\n          // 初始化时计算发票金额\r\n          this.$nextTick(() => {\r\n            this.calculateInvoiceAmount()\r\n          })\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听 form prop 变化，当父组件更新发票数据时自动同步到子组件\r\n    form: {\r\n      handler(newVal) {\r\n        if (newVal && Object.keys(newVal).length > 0) {\r\n          // 当 form prop 变化时，更新内部的 formData\r\n          this.formData = JSON.parse(JSON.stringify(newVal))\r\n\r\n          // 如果所属公司已有值，自动填充相关信息\r\n          if (this.formData.invoiceBelongsTo) {\r\n            this.autoFillCompanyInfo(this.formData.invoiceBelongsTo)\r\n          }\r\n\r\n          // 重新计算发票金额\r\n          this.$nextTick(() => {\r\n            this.calculateInvoiceAmount()\r\n          })\r\n        }\r\n      },\r\n      deep: true\r\n    },\r\n    // 监听 invoiceItems prop 变化，当父组件更新费用明细时自动同步到子组件\r\n    // invoiceItems: {\r\n    //   handler(newVal) {\r\n    //     if (newVal && Array.isArray(newVal)) {\r\n    //       // 当 invoiceItems prop 变化时，更新内部的 invoiceItemList\r\n    //       this.invoiceItemList = JSON.parse(JSON.stringify(newVal))\r\n\r\n    //       // 重新计算发票金额\r\n    //       this.$nextTick(() => {\r\n    //         this.calculateInvoiceAmount()\r\n    //       })\r\n    //     }\r\n    //   },\r\n    //   deep: true\r\n    // },\r\n    // 监听对方公司ID变化\r\n    \"formData.cooperatorId\": {\r\n      handler(newVal, oldVal) {\r\n        // 只有当值真正变化时才触发查询\r\n        this.debouncedFetchCompanyInfo(newVal)\r\n      }\r\n    },\r\n    // 监听所属公司变化，自动填充我司发票抬头和税号\r\n    \"formData.invoiceBelongsTo\": {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          this.autoFillCompanyInfo(newVal)\r\n        }\r\n      }\r\n    },\r\n    // 发票金额自动根据发票汇率/发票币种/发票明细表的含税小计计算\r\n    \"formData.invoiceCurrencyCode\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      }\r\n    },\r\n    // 监听发票汇率变化\r\n    \"formData.invoiceExchangeRate\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      }\r\n    },\r\n    // 监听费用列表变化\r\n    \"formData.rsChargeList\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      },\r\n      deep: true\r\n    },\r\n    // 监听发票类型变化\r\n    \"formData.invoiceType\": {\r\n      handler(newVal) {\r\n        this.calculateInvoiceAmount()\r\n      }\r\n    }\r\n  },\r\n  beforeMount() {\r\n    this.loadStaff()\r\n  },\r\n  methods: {\r\n    handleInvoiceAuditCancel() {\r\n      this.formData.auditStuffId = null\r\n      this.formData.auditTime = null\r\n      this.formData.auditStatus = null\r\n      // 清空发票流水号\r\n      this.formData.invoiceCodeNo = null\r\n      this.formData.rsChargeList.forEach(charge => {\r\n        charge.invoiceCodeNo = null\r\n      })\r\n      this.$emit(\"invoiceAuditCancel\", this.formData)\r\n    },\r\n    handleInvoiceApplyCancel() {\r\n      // 发票状态改为未开票\r\n      this.formData.applyStuffId = null\r\n      this.formData.appliedTime = null\r\n      this.formData.invoiceStatus = \"unissued\"\r\n\r\n      this.$emit(\"invoiceApplyCancel\", this.formData)\r\n    },\r\n    // 处理表格选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection\r\n    },\r\n\r\n    // 显示批量设置开票项目对话框\r\n    showBatchInvoicingItemDialog() {\r\n      this.batchForm.invoicingItem = null\r\n      this.batchInvoicingItemDialogVisible = true\r\n    },\r\n\r\n    // 确认批量设置开票项目\r\n    confirmBatchSetInvoicingItem() {\r\n      if (!this.batchForm.invoicingItem) {\r\n        this.$message.warning(\"请选择开票项目名称\")\r\n        return\r\n      }\r\n\r\n      // 为选中的行设置开票项目\r\n      this.selectedRows.forEach(row => {\r\n        row.invoicingItem = this.batchForm.invoicingItem\r\n      })\r\n\r\n      this.batchInvoicingItemDialogVisible = false\r\n      this.$message.success(`已为 ${this.selectedRows.length} 项设置开票项目`)\r\n\r\n      // 重新计算发票金额\r\n      this.calculateInvoiceAmount()\r\n    },\r\n\r\n    // 批量设置默认开票项目\r\n    batchSetDefaultInvoicingItem() {\r\n      // 根据费用名称智能设置默认开票项目\r\n      this.selectedRows.forEach(row => {\r\n        const chargeName = row.chargeName || \"\"\r\n\r\n        // 根据费用名称匹配默认开票项目\r\n        if (chargeName.includes(\"运费\") || chargeName.includes(\"运输\")) {\r\n          row.invoicingItem = \"代理运费\"\r\n        } else if (chargeName.includes(\"报关\")) {\r\n          row.invoicingItem = \"代理报关服务费\"\r\n        } else if (chargeName.includes(\"拖车\")) {\r\n          row.invoicingItem = \"代理拖车费\"\r\n        } else if (chargeName.includes(\"港杂\") || chargeName.includes(\"码头\")) {\r\n          row.invoicingItem = \"代理港杂费\"\r\n        } else if (chargeName.includes(\"文件\")) {\r\n          row.invoicingItem = \"代理文件费\"\r\n        } else if (chargeName.includes(\"操作\")) {\r\n          row.invoicingItem = \"代理操作费\"\r\n        } else {\r\n          // 默认设置为代理服务费\r\n          row.invoicingItem = \"代理服务费\"\r\n        }\r\n      })\r\n\r\n      this.$message.success(`已为 ${this.selectedRows.length} 项设置默认开票项目`)\r\n\r\n      // 重新计算发票金额\r\n      this.calculateInvoiceAmount()\r\n    },\r\n\r\n    // 批量清空开票项目\r\n    batchClearInvoicingItem() {\r\n      this.$confirm(\"确定要清空选中项的开票项目吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n        customClass: \"modal-confirm\"\r\n      }).then(() => {\r\n        this.selectedRows.forEach(row => {\r\n          row.invoicingItem = null\r\n        })\r\n\r\n        this.$message.success(`已清空 ${this.selectedRows.length} 项的开票项目`)\r\n\r\n        // 重新计算发票金额\r\n        this.calculateInvoiceAmount()\r\n      }).catch(() => {\r\n        // 用户取消操作\r\n      })\r\n    },\r\n\r\n    // 开票项目数据标准化函数\r\n    invoicingItemNormalizer(node) {\r\n      const normalized = {\r\n        id: node.invoicingItemName, // 使用invoicingItemName作为id\r\n        label: node.invoicingItemName,\r\n        invoicingItemName: node.invoicingItemName\r\n      }\r\n\r\n      if (node.children && node.children.length > 0) {\r\n        normalized.children = node.children.map(child => this.invoicingItemNormalizer(child))\r\n      }\r\n\r\n      return normalized\r\n    },\r\n\r\n    // 计算发票金额\r\n    calculateInvoiceAmount() {\r\n      // 检查必要的字段是否存在\r\n      if (!this.formData.rsChargeList || !Array.isArray(this.formData.rsChargeList)) {\r\n        this.formData.invoiceNetAmount = 0\r\n        return\r\n      }\r\n\r\n      // 计算费用明细的总金额和税额\r\n      // 税金\r\n      let vatAmount = this.formData.rsChargeList.reduce((acc, charge) => {\r\n        // 确保subtotal字段存在且为数字\r\n        const dutyRate = parseFloat(charge.dutyRate / 100) || 0\r\n        const vatAmount = dutyRate * (parseFloat(charge.dnAmount * charge.dnUnitRate) || 0)\r\n        return acc + vatAmount\r\n      }, 0)\r\n      // 含税金\r\n      let netAmount = this.formData.rsChargeList.reduce((acc, charge) => {\r\n        // 确保subtotal字段存在且为数字\r\n        const subtotal = parseFloat(charge.subtotal) || 0\r\n        return acc + subtotal\r\n      }, 0)\r\n      // 不含税金\r\n      let dnAmount = this.formData.rsChargeList.reduce((acc, charge) => {\r\n        // 确保subtotal字段存在且为数字\r\n        const subtotal = parseFloat(charge.dnAmount * charge.dnUnitRate) || 0\r\n        return acc + subtotal\r\n      }, 0)\r\n\r\n      // 保存应收/应付\r\n      if (this.formData.saleBuy === \"sale\") {\r\n        this.formData.dnSum = netAmount\r\n        this.formData.vatAmount = vatAmount\r\n      } else {\r\n        this.formData.cnSum = netAmount\r\n        this.formData.vatAmount = vatAmount\r\n      }\r\n\r\n      // 根据发票币种和汇率进行相应的计算\r\n      if (this.formData.invoiceCurrencyCode && this.formData.invoiceExchangeRate) {\r\n        const exchangeRate = parseFloat(this.formData.invoiceExchangeRate)\r\n        if (exchangeRate > 0) {\r\n          // 如果汇率大于0，则进行汇率转换\r\n          netAmount = netAmount * exchangeRate\r\n          dnAmount = dnAmount * exchangeRate\r\n          vatAmount = vatAmount * exchangeRate\r\n        }\r\n      }\r\n\r\n      // 如果是增值税专用发票，需要加上6%的税额\r\n      let taxAmount = 0\r\n      if (this.formData.invoiceType === \"增值税专用发票\") {\r\n        taxAmount = netAmount * 0.06\r\n        netAmount = netAmount + taxAmount\r\n      }\r\n\r\n      // 保留两位小数\r\n      // 不含税\r\n      this.formData.invoiceNetAmount = parseFloat(dnAmount.toFixed(2))\r\n      // 发票金额\r\n      this.formData.invoiceAmount = \"RMB\" + this.formatCurrency(dnAmount) + \" + \" + this.formatCurrency(vatAmount) + \" = \" + this.formatCurrency(netAmount)\r\n      // 结算金额\r\n      this.formData.settlementAmount = parseFloat(netAmount.toFixed(2))\r\n    },\r\n\r\n    // 获取发票金额占位符文本\r\n    getInvoiceAmountPlaceholder() {\r\n      if (!this.formData.rsChargeList || this.formData.rsChargeList.length === 0) {\r\n        return \"请先添加费用明细\"\r\n      }\r\n      if (!this.formData.invoiceCurrencyCode) {\r\n        return \"请选择发票币种\"\r\n      }\r\n      if (!this.formData.invoiceExchangeRate) {\r\n        return \"请输入发票汇率\"\r\n      }\r\n      return \"金额将自动计算\"\r\n    },\r\n\r\n    // 格式化货币显示\r\n    formatCurrency(amount) {\r\n      if (!amount && amount !== 0) return \"0.00\"\r\n      const num = parseFloat(amount)\r\n      if (isNaN(num)) return \"0.00\"\r\n      return num.toFixed(2)\r\n    },\r\n\r\n    // 提交表单\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          // 自动收集费用列表中的rct号，以逗号分隔填入相关订单号字段\r\n          this.collectRctNumbers()\r\n          this.$emit(\"submit\", this.formData)\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    handleCancel() {\r\n      this.$emit(\"cancel\")\r\n      this.handleClose()\r\n    },\r\n    // 关闭对话框\r\n    handleClose() {\r\n      this.$emit(\"update:visible\", false)\r\n    },\r\n    searchAvailableInvoice() {\r\n      console.log(\"检索可用发票\")\r\n    },\r\n\r\n    // 获取公司银行账户列表\r\n    fetchCompanyBankAccounts() {\r\n      // 检查是否有选择对方公司\r\n      if (!this.formData.cooperatorId) {\r\n        this.$message.warning(\"请先选择对方公司\")\r\n        return\r\n      }\r\n\r\n      // 调用API获取该公司的银行账户\r\n      listAccount({belongToCompany: this.formData.cooperatorId}).then(response => {\r\n        if (response.code === 200) {\r\n          this.companyBankList = response.rows || []\r\n          // 如果没有账户，显示提示\r\n          if (this.companyBankList.length === 0) {\r\n            this.$message.info(\"该公司没有银行账户记录\")\r\n          }\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取公司银行账户失败\", error)\r\n        this.$message.error(\"获取公司银行账户失败\")\r\n      })\r\n    },\r\n\r\n    // 处理银行账户选择变化\r\n    handleBankAccountChange(bankCode) {\r\n      // 根据选择的bankCode找到对应的银行账户信息\r\n      const selectedAccount = this.availableBankList.find(item => item.bankCode === bankCode)\r\n\r\n      if (selectedAccount) {\r\n        // 自动填充银行全称和银行账号\r\n        this.formData.cooperatorBankFullname = selectedAccount.bankBranchName || \"\"\r\n        this.formData.cooperatorBankAccount = selectedAccount.bankAccount || \"\"\r\n      }\r\n    },\r\n    // 获取公司信息\r\n    fetchCompanyInfo(companyId) {\r\n      if (!companyId) {\r\n        return\r\n      }\r\n      getCompany(companyId).then(response => {\r\n        if (response.code === 200) {\r\n          const companyInfo = response.data\r\n          // 更新表单中与对方公司相关的字段\r\n          this.formData.cooperatorCompanyTitle = companyInfo.companyLocalName || \"\"\r\n          this.formData.cooperatorVatSerialNo = companyInfo.taxNo || \"\"\r\n          this.formData.cooperatorShortName = companyInfo.companyShortName || \"\"\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取公司信息失败\", error)\r\n      })\r\n    },\r\n\r\n    // 自动填充我司发票抬头和税号\r\n    autoFillCompanyInfo(companyCode) {\r\n      // 根据所属公司代码(GZRS/HKRS/SZRS/GZCF)自动填充我司发票抬头和税号\r\n      const companyInfoMap = {\r\n        \"GZRS\": {\r\n          title: \"广州瑞旗国际货运代理有限公司\",\r\n          taxNo: \"91440101MA59UQXX7B\"\r\n        },\r\n        \"HKRS\": {\r\n          title: \"香港瑞旗国际货运代理有限公司\",\r\n          taxNo: \"HK12345678\"\r\n        },\r\n        \"SZRS\": {\r\n          title: \"深圳市瑞旗国际货运代理有限公司\",\r\n          taxNo: \"91440300MA5G9UB57Q\"\r\n        },\r\n        \"GZCF\": {\r\n          title: \"广州正泽国际货运代理有限公司\",\r\n          taxNo: \"91440101MA9XRGLH0F\"\r\n        }\r\n      }\r\n\r\n      // 获取对应公司的信息\r\n      const companyInfo = companyInfoMap[companyCode]\r\n\r\n      // 如果找到对应的公司信息，则填充表单\r\n      if (companyInfo) {\r\n        this.formData.richCompanyTitle = companyInfo.title\r\n        this.formData.richVatSerialNo = companyInfo.taxNo\r\n      }\r\n    },\r\n\r\n    // 判断表单项是否禁用\r\n    isDisabled() {\r\n      // 根据以下条件判断表单是否应该禁用：\r\n      // 1. 如果发票状态为已开票\r\n      if (this.type === \"debitNote\" && this.formData.invoiceStatus === \"applied\") {\r\n        return true\r\n      }\r\n\r\n      // 2. 如果报税已锁定\r\n      if (this.formData.taxLocked === \"1\") {\r\n        return true\r\n      }\r\n\r\n      // 3. 如果已支付\r\n      // if (this.formData.actualPayDate) {\r\n      //   return true\r\n      // }\r\n\r\n      // 如果发票已审核，则不可编辑\r\n      if (this.formData.auditStatus === \"审核通过\") {\r\n        return true\r\n      }\r\n\r\n      // 如果没有禁用条件，则表单可编辑\r\n      return false\r\n    },\r\n    handleRichBankCodeChange(row) {\r\n      this.formData.richBankFullname = row.bankBranchName\r\n      this.formData.richBankAccount = row.bankAccount\r\n    },\r\n    // 获取发票状态类型\r\n    getInvoiceStatusType(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"info\",\r\n        \"issued\": \"success\",\r\n        \"applied\": \"warning\",\r\n        \"canceled\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n    // 获取发票状态文本\r\n    getInvoiceStatusText(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"未开票\",\r\n        \"issued\": \"已开票\",\r\n        \"applied\": \"已申请\",\r\n        \"confirmed\": \"已审核\",\r\n        \"canceled\": \"已作废\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    },\r\n    // 申请开票\r\n    async handleApplyInvoice() {\r\n      try {\r\n        // 获取当前用户信息\r\n        const currentUser = this.$store.state.user\r\n        const currentTime = new Date().toISOString()\r\n\r\n        // 更新发票状态为已申请\r\n        const updateData = {\r\n          ...this.formData,\r\n          invoiceStatus: \"applied\",\r\n          applyStuffId: currentUser.sid, // 开票人ID\r\n          appliedTime: currentTime // 开票时间\r\n        }\r\n\r\n        // 调用API更新发票状态\r\n        const response = await updateVatinvoice(updateData)\r\n\r\n        if (response.code === 200) {\r\n          // 更新本地数据\r\n          this.formData.invoiceStatus = \"applied\"\r\n          this.formData.applyStuffId = currentUser.sid\r\n          this.formData.appliedTime = currentTime\r\n\r\n          this.$message.success(\"申请开票成功\")\r\n\r\n          // 通知父组件数据变化\r\n          this.$emit(\"applyInvoice\", this.formData)\r\n        } else {\r\n          this.$message.error(response.msg || \"申请开票失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"申请开票失败:\", error)\r\n        this.$message.error(\"申请开票失败，请重试\")\r\n      }\r\n    },\r\n    // 信息审核\r\n    async handleAuditInvoice() {\r\n      // 获取当前用户信息\r\n      const currentUser = this.$store.state.user\r\n      const currentTime = new Date().toISOString()\r\n\r\n      // 更新审核信息\r\n      this.formData = {\r\n        ...this.formData,\r\n        invoiceStatus: \"confirmed\",\r\n        auditStuffId: currentUser.sid, // 审核人ID\r\n        auditTime: currentTime, // 审核时间\r\n        auditStatus: \"审核通过\" // 审核状态\r\n      }\r\n\r\n      // 通知父组件数据变化\r\n      this.$emit(\"auditInvoice\", this.formData)\r\n    },\r\n    // 发送开票\r\n    async handleSendInvoice() {\r\n      try {\r\n        // 检查审核状态，只有审核通过的发票才能开票\r\n        if (this.formData.auditStatus !== \"审核通过\") {\r\n          this.$message.error(\"只有审核通过的发票才能进行开票操作\")\r\n          return\r\n        }\r\n\r\n        // 获取当前用户信息\r\n        const currentUser = this.$store.state.user\r\n        const currentTime = new Date().toISOString()\r\n\r\n        // 更新开票信息\r\n        const updateData = {\r\n          ...this.formData,\r\n          invoiceStatus: \"issued\", // 发票状态为已开票\r\n          issuedStuffId: currentUser.sid, // 开票人ID\r\n          issuedTime: currentTime // 开票时间\r\n        }\r\n\r\n        // 调用API更新发票开票信息\r\n        const response = await updateVatinvoice(updateData)\r\n\r\n        if (response.code === 200) {\r\n          // 更新本地数据\r\n          this.formData.invoiceStatus = \"issued\"\r\n          this.formData.issuedStuffId = currentUser.sid\r\n          this.formData.issuedTime = currentTime\r\n\r\n          this.$message.success(\"发送开票成功\")\r\n\r\n          // 通知父组件数据变化\r\n          this.$emit(\"sendInvoice\", this.formData)\r\n        } else {\r\n          this.$message.error(response.msg || \"发送开票失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"发送开票失败:\", error)\r\n        this.$message.error(\"发送开票失败，请重试\")\r\n      }\r\n    }\r\n    ,\r\n    // 判断是否可以申请开票\r\n    canApplyInvoice() {\r\n      // 检查是否有发票ID\r\n      if (!this.formData.invoiceId) {\r\n        return false\r\n      }\r\n\r\n      // 检查发票状态\r\n      if (this.formData.invoiceStatus === \"issued\") {\r\n        return false // 已开票不能再次申请\r\n      }\r\n\r\n      if (this.formData.invoiceStatus === \"applied\") {\r\n        return false // 已申请不能重复申请\r\n      }\r\n\r\n      // 检查是否已报税锁定\r\n      if (this.formData.taxLocked === \"1\") {\r\n        return false\r\n      }\r\n\r\n      // 检查是否已支付（如果需要的话）\r\n      // if (this.formData.actualPayDate) {\r\n      //   return false\r\n      // }\r\n\r\n      return true\r\n    }\r\n    ,\r\n    // 获取申请信息\r\n    getApplyInfo() {\r\n      if (this.formData.invoiceStatus === \"applied\") {\r\n        // 如果已申请开票，显示申请人+时间\r\n        const currentUser = this.$store.state.user\r\n        let applyTime = \"\"\r\n\r\n        if (this.formData.issuedTime) {\r\n          try {\r\n            applyTime = new Date(this.formData.issuedTime).toLocaleString(\"zh-CN\", {\r\n              year: \"numeric\",\r\n              month: \"2-digit\",\r\n              day: \"2-digit\",\r\n              hour: \"2-digit\",\r\n              minute: \"2-digit\"\r\n            })\r\n          } catch (e) {\r\n            applyTime = \"时间格式错误\"\r\n          }\r\n        }\r\n\r\n        const userName = this.getName(currentUser.sid) || \"未知用户\"\r\n        return `${userName}${applyTime}`\r\n      }\r\n      if (this.formData.invoiceStatus === \"issued\") {\r\n        return \"已开票\"\r\n      }\r\n      if (this.formData.taxLocked === \"1\") {\r\n        return \"已报税锁定\"\r\n      }\r\n      if (this.formData.actualPayDate) {\r\n        return \"已支付\"\r\n      }\r\n      return \"未申请\"\r\n    }\r\n    ,\r\n    getName(id) {\r\n      if (id) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff) {\r\n          return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n        } else {\r\n          return \"\"\r\n        }\r\n      } else {\r\n        return \"\"\r\n      }\r\n    }\r\n    ,\r\n    loadStaff() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === \"业务部\")\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === \"业务部\")\r\n      }\r\n    }\r\n    ,\r\n\r\n    // 自动收集费用列表中的rct号，以逗号分隔填入相关订单号字段\r\n    collectRctNumbers() {\r\n      if (!this.formData.rsChargeList || !Array.isArray(this.formData.rsChargeList)) {\r\n        return\r\n      }\r\n\r\n      // 收集所有不重复的rct号\r\n      const rctNumbers = new Set()\r\n      this.formData.rsChargeList.forEach(charge => {\r\n        if (charge.sqdRctNo && charge.sqdRctNo.trim()) {\r\n          rctNumbers.add(charge.sqdRctNo.trim())\r\n        }\r\n      })\r\n\r\n      // 将rct号转换为数组并排序，然后以逗号分隔\r\n      const rctArray = Array.from(rctNumbers).sort()\r\n      this.formData.relatedOrderNo = rctArray.join(\",\")\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.yellow-bg {\r\n  background-color: #fffbe6;\r\n}\r\n\r\n.disable-form {\r\n  background-color: #f5f7fa;\r\n  border-color: #e4e7ed;\r\n  color: #c0c4cc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.uploaded-file-preview {\r\n  margin-top: 10px;\r\n  padding: 10px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.preview-title {\r\n  font-weight: bold;\r\n  color: #495057;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.file-links {\r\n  display: flex;\r\n  gap: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.file-links .el-link {\r\n  font-size: 12px;\r\n}\r\n\r\n/* treeselect组件样式 */\r\n:deep(.vue-treeselect__menu) {\r\n  z-index: 9999 !important;\r\n  position: fixed !important;\r\n}\r\n\r\n:deep(.vue-treeselect__menu-container) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n:deep(.vue-treeselect__dropdown) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n/* 确保下拉框在表格之上 */\r\n:deep(.vue-treeselect__menu-arrow) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n:deep(.vue-treeselect__list) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n/* 金额公式样式 */\r\n.amount-formula {\r\n  margin-top: 5px;\r\n  padding: 5px 8px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n  border-left: 3px solid #409eff;\r\n}\r\n\r\n.amount-formula small {\r\n  color: #606266;\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n}\r\n\r\n.text-muted {\r\n  color: #909399 !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwjBA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACAA,OAAA;AACA,IAAAM,MAAA,GAAAF,sBAAA,CAAAJ,OAAA;AACA,IAAAO,UAAA,GAAAP,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA,SAAAQ,SAAAC,EAAA,EAAAC,KAAA;EACA,IAAAC,KAAA;EACA;IACA,IAAAC,OAAA;IACA,IAAAC,IAAA,GAAAC,SAAA;IACAC,YAAA,CAAAJ,KAAA;IACAA,KAAA,GAAAK,UAAA;MACAP,EAAA,CAAAQ,KAAA,CAAAL,OAAA,EAAAC,IAAA;IACA,GAAAH,KAAA;EACA;AACA;AAAA,IAAAQ,SAAA,GAEA;EACAC,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;IACA;IACAG,IAAA;MACAL,IAAA,EAAAM,MAAA;MACAJ,OAAA,WAAAK,SAAA;QAAA;MAAA;IACA;IACA;IACAC,KAAA;MACAR,IAAA,EAAAM,MAAA;MACAJ,OAAA,WAAAK,SAAA;QAAA;MAAA;IACA;IACA;IACAE,YAAA;MACAT,IAAA,EAAAU,KAAA;MACAR,OAAA,WAAAK,SAAA;QAAA;MAAA;IACA;IACAI,WAAA;MACAX,IAAA,EAAAU,KAAA;MACAR,OAAA,WAAAK,SAAA;QAAA;MAAA;IACA;IACA;IACAK,eAAA;MACAZ,IAAA,EAAAU,KAAA;MACAR,OAAA,WAAAK,SAAA;QAAA;MAAA;IACA;IACAP,IAAA;MACAA,IAAA,EAAAI,MAAA;MACAF,OAAA;IACA;EACA;EACAW,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,aAAA;MACA;MACAC,QAAA;MACA;MACAC,eAAA;MACA;MACAC,yBAAA;MACA;MACAC,eAAA;MACA;MACAC,oBAAA,GACA;QACAC,EAAA;QACAC,iBAAA;QACAC,QAAA,GACA;UAAAF,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA,GACA;UAAAD,EAAA;UAAAC,iBAAA;QAAA;MAEA,EACA;MACA;MACAE,YAAA;MAAA;MACAC,+BAAA;MAAA;MACAC,SAAA;QACAC,aAAA;MACA;IACA;EACA;;EACAC,QAAA;IACA;IACAC,iBAAA,WAAAA,kBAAA;MACA,YAAAV,eAAA,CAAAW,MAAA,YAAAX,eAAA,QAAAN,eAAA;IACA;EACA;EAEAkB,OAAA,WAAAA,QAAA;IACA;IACA,KAAAb,yBAAA,GAAAlC,QAAA,MAAAgD,gBAAA;EACA;EACAC,KAAA;IACAjC,OAAA;MACAkC,OAAA,WAAAA,QAAAC,GAAA;QAAA,IAAAC,KAAA;QACA,KAAArB,aAAA,GAAAoB,GAAA;QACA,IAAAA,GAAA;UACA;UACA,KAAAnB,QAAA,GAAAqB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAjC,IAAA;UACA,KAAAW,eAAA,GAAAoB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA7B,YAAA;;UAEA;UACA,UAAAM,QAAA,CAAAwB,iBAAA;YACA,KAAAxB,QAAA,CAAAwB,iBAAA;UACA;;UAEA;UACA,SAAAxB,QAAA,CAAAyB,gBAAA;YACA,KAAAC,mBAAA,MAAA1B,QAAA,CAAAyB,gBAAA;UACA;;UAEA;UACA,KAAAE,SAAA;YACAP,KAAA,CAAAQ,sBAAA;UACA;QACA;MACA;MACAC,SAAA;IACA;IACA;IACAvC,IAAA;MACA4B,OAAA,WAAAA,QAAAY,MAAA;QAAA,IAAAC,MAAA;QACA,IAAAD,MAAA,IAAAvC,MAAA,CAAAyC,IAAA,CAAAF,MAAA,EAAAhB,MAAA;UACA;UACA,KAAAd,QAAA,GAAAqB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAO,MAAA;;UAEA;UACA,SAAA9B,QAAA,CAAAyB,gBAAA;YACA,KAAAC,mBAAA,MAAA1B,QAAA,CAAAyB,gBAAA;UACA;;UAEA;UACA,KAAAE,SAAA;YACAI,MAAA,CAAAH,sBAAA;UACA;QACA;MACA;MACAK,IAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACAf,OAAA,WAAAA,QAAAY,MAAA,EAAAI,MAAA;QACA;QACA,KAAAhC,yBAAA,CAAA4B,MAAA;MACA;IACA;IACA;IACA;MACAZ,OAAA,WAAAA,QAAAY,MAAA;QACA,IAAAA,MAAA;UACA,KAAAJ,mBAAA,CAAAI,MAAA;QACA;MACA;IACA;IACA;IACA;MACAZ,OAAA,WAAAA,QAAAY,MAAA;QACA,KAAAF,sBAAA;MACA;IACA;IACA;IACA;MACAV,OAAA,WAAAA,QAAAY,MAAA;QACA,KAAAF,sBAAA;MACA;IACA;IACA;IACA;MACAV,OAAA,WAAAA,QAAAY,MAAA;QACA,KAAAF,sBAAA;MACA;MACAK,IAAA;IACA;IACA;IACA;MACAf,OAAA,WAAAA,QAAAY,MAAA;QACA,KAAAF,sBAAA;MACA;IACA;EACA;EACAO,WAAA,WAAAA,YAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAC,wBAAA,WAAAA,yBAAA;MACA,KAAAtC,QAAA,CAAAuC,YAAA;MACA,KAAAvC,QAAA,CAAAwC,SAAA;MACA,KAAAxC,QAAA,CAAAyC,WAAA;MACA;MACA,KAAAzC,QAAA,CAAA0C,aAAA;MACA,KAAA1C,QAAA,CAAA2C,YAAA,CAAAC,OAAA,WAAAC,MAAA;QACAA,MAAA,CAAAH,aAAA;MACA;MACA,KAAAI,KAAA,4BAAA9C,QAAA;IACA;IACA+C,wBAAA,WAAAA,yBAAA;MACA;MACA,KAAA/C,QAAA,CAAAgD,YAAA;MACA,KAAAhD,QAAA,CAAAiD,WAAA;MACA,KAAAjD,QAAA,CAAAkD,aAAA;MAEA,KAAAJ,KAAA,4BAAA9C,QAAA;IACA;IACA;IACAmD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5C,YAAA,GAAA4C,SAAA;IACA;IAEA;IACAC,4BAAA,WAAAA,6BAAA;MACA,KAAA3C,SAAA,CAAAC,aAAA;MACA,KAAAF,+BAAA;IACA;IAEA;IACA6C,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,MAAA;MACA,UAAA7C,SAAA,CAAAC,aAAA;QACA,KAAA6C,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,KAAAjD,YAAA,CAAAoC,OAAA,WAAAc,GAAA;QACAA,GAAA,CAAA/C,aAAA,GAAA4C,MAAA,CAAA7C,SAAA,CAAAC,aAAA;MACA;MAEA,KAAAF,+BAAA;MACA,KAAA+C,QAAA,CAAAG,OAAA,iBAAAC,MAAA,MAAApD,YAAA,CAAAM,MAAA;;MAEA;MACA,KAAAc,sBAAA;IACA;IAEA;IACAiC,4BAAA,WAAAA,6BAAA;MACA;MACA,KAAArD,YAAA,CAAAoC,OAAA,WAAAc,GAAA;QACA,IAAAI,UAAA,GAAAJ,GAAA,CAAAI,UAAA;;QAEA;QACA,IAAAA,UAAA,CAAAC,QAAA,UAAAD,UAAA,CAAAC,QAAA;UACAL,GAAA,CAAA/C,aAAA;QACA,WAAAmD,UAAA,CAAAC,QAAA;UACAL,GAAA,CAAA/C,aAAA;QACA,WAAAmD,UAAA,CAAAC,QAAA;UACAL,GAAA,CAAA/C,aAAA;QACA,WAAAmD,UAAA,CAAAC,QAAA,UAAAD,UAAA,CAAAC,QAAA;UACAL,GAAA,CAAA/C,aAAA;QACA,WAAAmD,UAAA,CAAAC,QAAA;UACAL,GAAA,CAAA/C,aAAA;QACA,WAAAmD,UAAA,CAAAC,QAAA;UACAL,GAAA,CAAA/C,aAAA;QACA;UACA;UACA+C,GAAA,CAAA/C,aAAA;QACA;MACA;MAEA,KAAA6C,QAAA,CAAAG,OAAA,iBAAAC,MAAA,MAAApD,YAAA,CAAAM,MAAA;;MAEA;MACA,KAAAc,sBAAA;IACA;IAEA;IACAoC,uBAAA,WAAAA,wBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAnF,IAAA;QACAoF,WAAA;MACA,GAAAC,IAAA;QACAL,MAAA,CAAAzD,YAAA,CAAAoC,OAAA,WAAAc,GAAA;UACAA,GAAA,CAAA/C,aAAA;QACA;QAEAsD,MAAA,CAAAT,QAAA,CAAAG,OAAA,uBAAAC,MAAA,CAAAK,MAAA,CAAAzD,YAAA,CAAAM,MAAA;;QAEA;QACAmD,MAAA,CAAArC,sBAAA;MACA,GAAA2C,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,UAAA;QACAtE,EAAA,EAAAoE,IAAA,CAAAnE,iBAAA;QAAA;QACAsE,KAAA,EAAAH,IAAA,CAAAnE,iBAAA;QACAA,iBAAA,EAAAmE,IAAA,CAAAnE;MACA;MAEA,IAAAmE,IAAA,CAAAlE,QAAA,IAAAkE,IAAA,CAAAlE,QAAA,CAAAO,MAAA;QACA6D,UAAA,CAAApE,QAAA,GAAAkE,IAAA,CAAAlE,QAAA,CAAAsE,GAAA,WAAAC,KAAA;UAAA,OAAAJ,MAAA,CAAAF,uBAAA,CAAAM,KAAA;QAAA;MACA;MAEA,OAAAH,UAAA;IACA;IAEA;IACA/C,sBAAA,WAAAA,uBAAA;MACA;MACA,UAAA5B,QAAA,CAAA2C,YAAA,KAAAhD,KAAA,CAAAoF,OAAA,MAAA/E,QAAA,CAAA2C,YAAA;QACA,KAAA3C,QAAA,CAAAgF,gBAAA;QACA;MACA;;MAEA;MACA;MACA,IAAAC,SAAA,QAAAjF,QAAA,CAAA2C,YAAA,CAAAuC,MAAA,WAAAC,GAAA,EAAAtC,MAAA;QACA;QACA,IAAAuC,QAAA,GAAAC,UAAA,CAAAxC,MAAA,CAAAuC,QAAA;QACA,IAAAH,SAAA,GAAAG,QAAA,IAAAC,UAAA,CAAAxC,MAAA,CAAAyC,QAAA,GAAAzC,MAAA,CAAA0C,UAAA;QACA,OAAAJ,GAAA,GAAAF,SAAA;MACA;MACA;MACA,IAAAO,SAAA,QAAAxF,QAAA,CAAA2C,YAAA,CAAAuC,MAAA,WAAAC,GAAA,EAAAtC,MAAA;QACA;QACA,IAAA4C,QAAA,GAAAJ,UAAA,CAAAxC,MAAA,CAAA4C,QAAA;QACA,OAAAN,GAAA,GAAAM,QAAA;MACA;MACA;MACA,IAAAH,QAAA,QAAAtF,QAAA,CAAA2C,YAAA,CAAAuC,MAAA,WAAAC,GAAA,EAAAtC,MAAA;QACA;QACA,IAAA4C,QAAA,GAAAJ,UAAA,CAAAxC,MAAA,CAAAyC,QAAA,GAAAzC,MAAA,CAAA0C,UAAA;QACA,OAAAJ,GAAA,GAAAM,QAAA;MACA;;MAEA;MACA,SAAAzF,QAAA,CAAA0F,OAAA;QACA,KAAA1F,QAAA,CAAA2F,KAAA,GAAAH,SAAA;QACA,KAAAxF,QAAA,CAAAiF,SAAA,GAAAA,SAAA;MACA;QACA,KAAAjF,QAAA,CAAA4F,KAAA,GAAAJ,SAAA;QACA,KAAAxF,QAAA,CAAAiF,SAAA,GAAAA,SAAA;MACA;;MAEA;MACA,SAAAjF,QAAA,CAAA6F,mBAAA,SAAA7F,QAAA,CAAA8F,mBAAA;QACA,IAAAC,YAAA,GAAAV,UAAA,MAAArF,QAAA,CAAA8F,mBAAA;QACA,IAAAC,YAAA;UACA;UACAP,SAAA,GAAAA,SAAA,GAAAO,YAAA;UACAT,QAAA,GAAAA,QAAA,GAAAS,YAAA;UACAd,SAAA,GAAAA,SAAA,GAAAc,YAAA;QACA;MACA;;MAEA;MACA,IAAAC,SAAA;MACA,SAAAhG,QAAA,CAAAiG,WAAA;QACAD,SAAA,GAAAR,SAAA;QACAA,SAAA,GAAAA,SAAA,GAAAQ,SAAA;MACA;;MAEA;MACA;MACA,KAAAhG,QAAA,CAAAgF,gBAAA,GAAAK,UAAA,CAAAC,QAAA,CAAAY,OAAA;MACA;MACA,KAAAlG,QAAA,CAAAmG,aAAA,gBAAAC,cAAA,CAAAd,QAAA,iBAAAc,cAAA,CAAAnB,SAAA,iBAAAmB,cAAA,CAAAZ,SAAA;MACA;MACA,KAAAxF,QAAA,CAAAqG,gBAAA,GAAAhB,UAAA,CAAAG,SAAA,CAAAU,OAAA;IACA;IAEA;IACAI,2BAAA,WAAAA,4BAAA;MACA,UAAAtG,QAAA,CAAA2C,YAAA,SAAA3C,QAAA,CAAA2C,YAAA,CAAA7B,MAAA;QACA;MACA;MACA,UAAAd,QAAA,CAAA6F,mBAAA;QACA;MACA;MACA,UAAA7F,QAAA,CAAA8F,mBAAA;QACA;MACA;MACA;IACA;IAEA;IACAM,cAAA,WAAAA,eAAAG,MAAA;MACA,KAAAA,MAAA,IAAAA,MAAA;MACA,IAAAC,GAAA,GAAAnB,UAAA,CAAAkB,MAAA;MACA,IAAAE,KAAA,CAAAD,GAAA;MACA,OAAAA,GAAA,CAAAN,OAAA;IACA;IAEA;IACAQ,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAtH,IAAA,CAAAuH,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAH,MAAA,CAAAI,iBAAA;UACAJ,MAAA,CAAA7D,KAAA,WAAA6D,MAAA,CAAA3G,QAAA;QACA;MACA;IACA;IACA;IACAgH,YAAA,WAAAA,aAAA;MACA,KAAAlE,KAAA;MACA,KAAAmE,WAAA;IACA;IACA;IACAA,WAAA,WAAAA,YAAA;MACA,KAAAnE,KAAA;IACA;IACAoE,sBAAA,WAAAA,uBAAA;MACAC,OAAA,CAAAC,GAAA;IACA;IAEA;IACAC,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAtH,QAAA,CAAAuH,YAAA;QACA,KAAA/D,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,IAAA+D,oBAAA;QAAAC,eAAA,OAAAzH,QAAA,CAAAuH;MAAA,GAAAjD,IAAA,WAAAoD,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAL,MAAA,CAAAnH,eAAA,GAAAuH,QAAA,CAAAE,IAAA;UACA;UACA,IAAAN,MAAA,CAAAnH,eAAA,CAAAW,MAAA;YACAwG,MAAA,CAAA9D,QAAA,CAAAqE,IAAA;UACA;QACA;MACA,GAAAtD,KAAA,WAAAuD,KAAA;QACAX,OAAA,CAAAW,KAAA,eAAAA,KAAA;QACAR,MAAA,CAAA9D,QAAA,CAAAsE,KAAA;MACA;IACA;IAEA;IACAC,uBAAA,WAAAA,wBAAAC,QAAA;MACA;MACA,IAAAC,eAAA,QAAApH,iBAAA,CAAAqH,IAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAH,QAAA,KAAAA,QAAA;MAAA;MAEA,IAAAC,eAAA;QACA;QACA,KAAAjI,QAAA,CAAAoI,sBAAA,GAAAH,eAAA,CAAAI,cAAA;QACA,KAAArI,QAAA,CAAAsI,qBAAA,GAAAL,eAAA,CAAAM,WAAA;MACA;IACA;IACA;IACAvH,gBAAA,WAAAA,iBAAAwH,SAAA;MAAA,IAAAC,MAAA;MACA,KAAAD,SAAA;QACA;MACA;MACA,IAAAE,mBAAA,EAAAF,SAAA,EAAAlE,IAAA,WAAAoD,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACA,IAAAgB,WAAA,GAAAjB,QAAA,CAAA5H,IAAA;UACA;UACA2I,MAAA,CAAAzI,QAAA,CAAA4I,sBAAA,GAAAD,WAAA,CAAAE,gBAAA;UACAJ,MAAA,CAAAzI,QAAA,CAAA8I,qBAAA,GAAAH,WAAA,CAAAI,KAAA;UACAN,MAAA,CAAAzI,QAAA,CAAAgJ,mBAAA,GAAAL,WAAA,CAAAM,gBAAA;QACA;MACA,GAAA1E,KAAA,WAAAuD,KAAA;QACAX,OAAA,CAAAW,KAAA,aAAAA,KAAA;MACA;IACA;IAEA;IACApG,mBAAA,WAAAA,oBAAAwH,WAAA;MACA;MACA,IAAAC,cAAA;QACA;UACA/J,KAAA;UACA2J,KAAA;QACA;QACA;UACA3J,KAAA;UACA2J,KAAA;QACA;QACA;UACA3J,KAAA;UACA2J,KAAA;QACA;QACA;UACA3J,KAAA;UACA2J,KAAA;QACA;MACA;;MAEA;MACA,IAAAJ,WAAA,GAAAQ,cAAA,CAAAD,WAAA;;MAEA;MACA,IAAAP,WAAA;QACA,KAAA3I,QAAA,CAAAoJ,gBAAA,GAAAT,WAAA,CAAAvJ,KAAA;QACA,KAAAY,QAAA,CAAAqJ,eAAA,GAAAV,WAAA,CAAAI,KAAA;MACA;IACA;IAEA;IACAO,UAAA,WAAAA,WAAA;MACA;MACA;MACA,SAAArK,IAAA,yBAAAe,QAAA,CAAAkD,aAAA;QACA;MACA;;MAEA;MACA,SAAAlD,QAAA,CAAAuJ,SAAA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA,SAAAvJ,QAAA,CAAAyC,WAAA;QACA;MACA;;MAEA;MACA;IACA;IACA+G,wBAAA,WAAAA,yBAAA9F,GAAA;MACA,KAAA1D,QAAA,CAAAyJ,gBAAA,GAAA/F,GAAA,CAAA2E,cAAA;MACA,KAAArI,QAAA,CAAA0J,eAAA,GAAAhG,GAAA,CAAA6E,WAAA;IACA;IACA;IACAoB,oBAAA,WAAAA,qBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IACA;IACAE,oBAAA,WAAAA,qBAAAF,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IACA;IACAG,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAA9K,OAAA,oBAAA+K,oBAAA,CAAA/K,OAAA,IAAAgL,IAAA,UAAAC,QAAA;QAAA,IAAAC,WAAA,EAAAC,WAAA,EAAAC,UAAA,EAAA7C,QAAA;QAAA,WAAAwC,oBAAA,CAAA/K,OAAA,IAAAqL,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAEA;cACAN,WAAA,GAAAL,MAAA,CAAAa,MAAA,CAAAC,KAAA,CAAAC,IAAA;cACAT,WAAA,OAAAU,IAAA,GAAAC,WAAA,IAEA;cACAV,UAAA,OAAAW,cAAA,CAAA/L,OAAA,MAAA+L,cAAA,CAAA/L,OAAA,MACA6K,MAAA,CAAAhK,QAAA;gBACAkD,aAAA;gBACAF,YAAA,EAAAqH,WAAA,CAAAc,GAAA;gBAAA;gBACAlI,WAAA,EAAAqH,WAAA;cAAA,IAGA;cAAAI,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAQ,4BAAA,EAAAb,UAAA;YAAA;cAAA7C,QAAA,GAAAgD,QAAA,CAAAW,IAAA;cAEA,IAAA3D,QAAA,CAAAC,IAAA;gBACA;gBACAqC,MAAA,CAAAhK,QAAA,CAAAkD,aAAA;gBACA8G,MAAA,CAAAhK,QAAA,CAAAgD,YAAA,GAAAqH,WAAA,CAAAc,GAAA;gBACAnB,MAAA,CAAAhK,QAAA,CAAAiD,WAAA,GAAAqH,WAAA;gBAEAN,MAAA,CAAAxG,QAAA,CAAAG,OAAA;;gBAEA;gBACAqG,MAAA,CAAAlH,KAAA,iBAAAkH,MAAA,CAAAhK,QAAA;cACA;gBACAgK,MAAA,CAAAxG,QAAA,CAAAsE,KAAA,CAAAJ,QAAA,CAAA4D,GAAA;cACA;cAAAZ,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAa,EAAA,GAAAb,QAAA;cAEAvD,OAAA,CAAAW,KAAA,YAAA4C,QAAA,CAAAa,EAAA;cACAvB,MAAA,CAAAxG,QAAA,CAAAsE,KAAA;YAAA;YAAA;cAAA,OAAA4C,QAAA,CAAAc,IAAA;UAAA;QAAA,GAAApB,OAAA;MAAA;IAEA;IACA;IACAqB,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzB,kBAAA,CAAA9K,OAAA,oBAAA+K,oBAAA,CAAA/K,OAAA,IAAAgL,IAAA,UAAAwB,SAAA;QAAA,IAAAtB,WAAA,EAAAC,WAAA;QAAA,WAAAJ,oBAAA,CAAA/K,OAAA,IAAAqL,IAAA,UAAAoB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlB,IAAA,GAAAkB,SAAA,CAAAjB,IAAA;YAAA;cACA;cACAP,WAAA,GAAAqB,OAAA,CAAAb,MAAA,CAAAC,KAAA,CAAAC,IAAA;cACAT,WAAA,OAAAU,IAAA,GAAAC,WAAA,IAEA;cACAS,OAAA,CAAA1L,QAAA,OAAAkL,cAAA,CAAA/L,OAAA,MAAA+L,cAAA,CAAA/L,OAAA,MACAuM,OAAA,CAAA1L,QAAA;gBACAkD,aAAA;gBACAX,YAAA,EAAA8H,WAAA,CAAAc,GAAA;gBAAA;gBACA3I,SAAA,EAAA8H,WAAA;gBAAA;gBACA7H,WAAA;cAAA,EACA;;cAEA;cACAiJ,OAAA,CAAA5I,KAAA,iBAAA4I,OAAA,CAAA1L,QAAA;YAAA;YAAA;cAAA,OAAA6L,SAAA,CAAAL,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACA;IACAG,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9B,kBAAA,CAAA9K,OAAA,oBAAA+K,oBAAA,CAAA/K,OAAA,IAAAgL,IAAA,UAAA6B,SAAA;QAAA,IAAA3B,WAAA,EAAAC,WAAA,EAAAC,UAAA,EAAA7C,QAAA;QAAA,WAAAwC,oBAAA,CAAA/K,OAAA,IAAAqL,IAAA,UAAAyB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvB,IAAA,GAAAuB,SAAA,CAAAtB,IAAA;YAAA;cAAAsB,SAAA,CAAAvB,IAAA;cAAA,MAGAoB,OAAA,CAAA/L,QAAA,CAAAyC,WAAA;gBAAAyJ,SAAA,CAAAtB,IAAA;gBAAA;cAAA;cACAmB,OAAA,CAAAvI,QAAA,CAAAsE,KAAA;cAAA,OAAAoE,SAAA,CAAAC,MAAA;YAAA;cAIA;cACA9B,WAAA,GAAA0B,OAAA,CAAAlB,MAAA,CAAAC,KAAA,CAAAC,IAAA;cACAT,WAAA,OAAAU,IAAA,GAAAC,WAAA,IAEA;cACAV,UAAA,OAAAW,cAAA,CAAA/L,OAAA,MAAA+L,cAAA,CAAA/L,OAAA,MACA4M,OAAA,CAAA/L,QAAA;gBACAkD,aAAA;gBAAA;gBACAkJ,aAAA,EAAA/B,WAAA,CAAAc,GAAA;gBAAA;gBACAkB,UAAA,EAAA/B,WAAA;cAAA,IAGA;cAAA4B,SAAA,CAAAtB,IAAA;cAAA,OACA,IAAAQ,4BAAA,EAAAb,UAAA;YAAA;cAAA7C,QAAA,GAAAwE,SAAA,CAAAb,IAAA;cAEA,IAAA3D,QAAA,CAAAC,IAAA;gBACA;gBACAoE,OAAA,CAAA/L,QAAA,CAAAkD,aAAA;gBACA6I,OAAA,CAAA/L,QAAA,CAAAoM,aAAA,GAAA/B,WAAA,CAAAc,GAAA;gBACAY,OAAA,CAAA/L,QAAA,CAAAqM,UAAA,GAAA/B,WAAA;gBAEAyB,OAAA,CAAAvI,QAAA,CAAAG,OAAA;;gBAEA;gBACAoI,OAAA,CAAAjJ,KAAA,gBAAAiJ,OAAA,CAAA/L,QAAA;cACA;gBACA+L,OAAA,CAAAvI,QAAA,CAAAsE,KAAA,CAAAJ,QAAA,CAAA4D,GAAA;cACA;cAAAY,SAAA,CAAAtB,IAAA;cAAA;YAAA;cAAAsB,SAAA,CAAAvB,IAAA;cAAAuB,SAAA,CAAAX,EAAA,GAAAW,SAAA;cAEA/E,OAAA,CAAAW,KAAA,YAAAoE,SAAA,CAAAX,EAAA;cACAQ,OAAA,CAAAvI,QAAA,CAAAsE,KAAA;YAAA;YAAA;cAAA,OAAAoE,SAAA,CAAAV,IAAA;UAAA;QAAA,GAAAQ,QAAA;MAAA;IAEA;IAEA;IACAM,eAAA,WAAAA,gBAAA;MACA;MACA,UAAAtM,QAAA,CAAAuM,SAAA;QACA;MACA;;MAEA;MACA,SAAAvM,QAAA,CAAAkD,aAAA;QACA;MACA;;MAEA,SAAAlD,QAAA,CAAAkD,aAAA;QACA;MACA;;MAEA;MACA,SAAAlD,QAAA,CAAAuJ,SAAA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;IACA;IAEA;IACAiD,YAAA,WAAAA,aAAA;MACA,SAAAxM,QAAA,CAAAkD,aAAA;QACA;QACA,IAAAmH,WAAA,QAAAQ,MAAA,CAAAC,KAAA,CAAAC,IAAA;QACA,IAAA0B,SAAA;QAEA,SAAAzM,QAAA,CAAAqM,UAAA;UACA;YACAI,SAAA,OAAAzB,IAAA,MAAAhL,QAAA,CAAAqM,UAAA,EAAAK,cAAA;cACAC,IAAA;cACAC,KAAA;cACAC,GAAA;cACAC,IAAA;cACAC,MAAA;YACA;UACA,SAAAC,CAAA;YACAP,SAAA;UACA;QACA;QAEA,IAAAQ,QAAA,QAAAC,OAAA,CAAA7C,WAAA,CAAAc,GAAA;QACA,UAAAvH,MAAA,CAAAqJ,QAAA,EAAArJ,MAAA,CAAA6I,SAAA;MACA;MACA,SAAAzM,QAAA,CAAAkD,aAAA;QACA;MACA;MACA,SAAAlD,QAAA,CAAAuJ,SAAA;QACA;MACA;MACA,SAAAvJ,QAAA,CAAAmN,aAAA;QACA;MACA;MACA;IACA;IAEAD,OAAA,WAAAA,QAAA7M,EAAA;MACA,IAAAA,EAAA;QACA,IAAA+M,KAAA,QAAAvC,MAAA,CAAAC,KAAA,CAAAhL,IAAA,CAAAuN,cAAA,CAAAC,MAAA,WAAAC,OAAA;UAAA,OAAAA,OAAA,CAAAC,OAAA,IAAAnN,EAAA;QAAA;QACA,IAAA+M,KAAA;UACA,OAAAA,KAAA,CAAAK,oBAAA,GAAAL,KAAA,CAAAM,oBAAA,GAAAN,KAAA,CAAAO,cAAA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEAvL,SAAA,WAAAA,UAAA;MAAA,IAAAwL,OAAA;MACA,SAAA/C,MAAA,CAAAC,KAAA,CAAAhL,IAAA,CAAAuN,cAAA,CAAAvM,MAAA,cAAA+J,MAAA,CAAAC,KAAA,CAAAhL,IAAA,CAAA+N,SAAA,CAAAR,cAAA;QACAS,cAAA,CAAAC,QAAA,sBAAAzJ,IAAA;UACAsJ,OAAA,CAAAI,SAAA,GAAAJ,OAAA,CAAA/C,MAAA,CAAAC,KAAA,CAAAhL,IAAA,CAAAuN,cAAA,CAAAC,MAAA,WAAAnF,IAAA;YAAA,OAAAA,IAAA,CAAA8F,IAAA,CAAAC,aAAA;UAAA;QACA;MACA;QACA,KAAAF,SAAA,QAAAnD,MAAA,CAAAC,KAAA,CAAAhL,IAAA,CAAAuN,cAAA,CAAAC,MAAA,WAAAnF,IAAA;UAAA,OAAAA,IAAA,CAAA8F,IAAA,CAAAC,aAAA;QAAA;MACA;IACA;IAGA;IACAnH,iBAAA,WAAAA,kBAAA;MACA,UAAA/G,QAAA,CAAA2C,YAAA,KAAAhD,KAAA,CAAAoF,OAAA,MAAA/E,QAAA,CAAA2C,YAAA;QACA;MACA;;MAEA;MACA,IAAAwL,UAAA,OAAAC,GAAA;MACA,KAAApO,QAAA,CAAA2C,YAAA,CAAAC,OAAA,WAAAC,MAAA;QACA,IAAAA,MAAA,CAAAwL,QAAA,IAAAxL,MAAA,CAAAwL,QAAA,CAAAC,IAAA;UACAH,UAAA,CAAAI,GAAA,CAAA1L,MAAA,CAAAwL,QAAA,CAAAC,IAAA;QACA;MACA;;MAEA;MACA,IAAAE,QAAA,GAAA7O,KAAA,CAAA8O,IAAA,CAAAN,UAAA,EAAAO,IAAA;MACA,KAAA1O,QAAA,CAAA2O,cAAA,GAAAH,QAAA,CAAAI,IAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAA1P,OAAA,GAAAT,SAAA"}]}