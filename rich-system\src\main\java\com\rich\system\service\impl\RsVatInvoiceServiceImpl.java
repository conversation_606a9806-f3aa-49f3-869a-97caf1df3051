package com.rich.system.service.impl;

import com.rich.common.core.domain.entity.RsCharge;
import com.rich.common.core.domain.entity.RsDebitNote;
import com.rich.common.core.domain.entity.RsVatInvoice;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.system.mapper.RsChargeMapper;
import com.rich.system.mapper.RsVatInvoiceMapper;
import com.rich.system.mapper.RsDebitNoteMapper;
import com.rich.system.service.RsChargeService;
import com.rich.system.service.RsDebitNoteService;
import com.rich.system.service.RsVatInvoiceService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * 发票登记Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Service
public class RsVatInvoiceServiceImpl implements RsVatInvoiceService {

    private static final Logger log = LoggerFactory.getLogger(RsVatInvoiceServiceImpl.class);

    // 缓存RCT发票数量，避免重复查询数据库
    private final ConcurrentHashMap<Long, Integer> rctCountCache = new ConcurrentHashMap<>();

    // 缓存合作方发票数量
    private final ConcurrentHashMap<String, Integer> cooperatorCountCache = new ConcurrentHashMap<>();

    // 用于同步的锁
    private final ReentrantLock codeGenerationLock = new ReentrantLock();

    @Autowired
    private RsVatInvoiceMapper rsVatInvoiceMapper;

    @Autowired
    private RsChargeService rsChargeService;

    @Autowired
    private RsChargeMapper rsChargeMapper;

    @Autowired
    private RsDebitNoteService rsDebitNoteService;

    @Autowired
    private RsDebitNoteMapper rsDebitNoteMapper;

    /**
     * 查询发票登记
     *
     * @param invoiceId 发票登记主键
     * @return 发票登记
     */
    @Override
    public RsVatInvoice selectRsVatInvoiceByInvoiceId(Long invoiceId) {
        return rsVatInvoiceMapper.selectRsVatInvoiceByInvoiceId(invoiceId);
    }

    /**
     * 查询发票登记列表
     *
     * @param rsVatInvoice 发票登记
     * @return 发票登记
     */
    @Override
    public List<RsVatInvoice> selectRsVatInvoiceList(RsVatInvoice rsVatInvoice) {
        return rsVatInvoiceMapper.selectRsVatInvoiceList(rsVatInvoice);
    }

    /**
     * 新增发票登记
     *
     * @param rsVatInvoice 发票登记
     * @return 发票登记对象（包含生成的invoiceId）
     */
    @Override
    public RsVatInvoice insertRsVatInvoice(RsVatInvoice rsVatInvoice) {
        rsVatInvoice.setCreateTime(DateUtils.getNowDate());
        rsVatInvoice.setCreateBy(SecurityUtils.getUserId());
        rsVatInvoiceMapper.insertRsVatInvoice(rsVatInvoice);
        List<RsCharge> rsCharges = rsVatInvoice.getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsChargeMapper.upsertRsCharge(rsCharge);
        }

        // 清除相关缓存
        clearCache();

        return rsVatInvoice; // 返回包含invoiceId的完整对象
    }

    @Override
    public RsVatInvoice insertMergeRsVatInvoice(RsVatInvoice rsVatInvoice, List<RsVatInvoice> invoices) {
        // 这个方法在新的合并逻辑中不再使用，保留以兼容现有代码
        rsVatInvoice.setCreateTime(DateUtils.getNowDate());
        rsVatInvoice.setCreateBy(SecurityUtils.getUserId());
        rsVatInvoiceMapper.insertRsVatInvoice(rsVatInvoice);

        // 清除相关缓存
        clearCache();

        return rsVatInvoice; // 返回包含invoiceId的完整对象
    }

    /**
     * 修改发票登记
     *
     * @param rsVatInvoice 发票登记
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRsVatInvoice(RsVatInvoice rsVatInvoice) {
        rsVatInvoice.setUpdateTime(DateUtils.getNowDate());
        rsVatInvoice.setUpdateBy(SecurityUtils.getUserId());
        int result = rsVatInvoiceMapper.updateRsVatInvoice(rsVatInvoice);
        List<RsCharge> rsCharges = rsVatInvoice.getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsChargeMapper.upsertRsCharge(rsCharge);
        }


        // 清除相关缓存
        if (result > 0) {
            clearCache();
        }

        return result;
    }

    /**
     * 修改发票登记状态
     *
     * @param rsVatInvoice 发票登记
     * @return 发票登记
     */
    @Override
    public int changeStatus(RsVatInvoice rsVatInvoice) {
        return rsVatInvoiceMapper.updateRsVatInvoice(rsVatInvoice);
    }

    /**
     * 批量删除发票登记
     *
     * @param invoiceIds 需要删除的发票登记主键
     * @return 结果
     */
    @Override
    public int deleteRsVatInvoiceByInvoiceIds(Long[] invoiceIds) {
        int result = rsVatInvoiceMapper.deleteRsVatInvoiceByInvoiceIds(invoiceIds);

        // 清除相关缓存
        if (result > 0) {
            clearCache();
        }

        return result;
    }

    /**
     * 删除发票登记信息
     *
     * @param invoiceId 发票登记主键
     * @return 结果
     */
    @Override
    public int deleteRsVatInvoiceByInvoiceId(Long invoiceId) {
        int result = rsVatInvoiceMapper.deleteRsVatInvoiceByInvoiceId(invoiceId);

        // 清除相关缓存
        if (result > 0) {
            clearCache();
        }

        return result;
    }

    /**
     * 根据rctId查询发票数量
     *
     * @param rctId RCT主键
     * @return 发票数量
     */
    @Override
    public int countRsVatInvoiceByRctId(Long rctId) {
        return rsVatInvoiceMapper.countRsVatInvoiceByRctId(rctId);
    }

    /**
     * 根据rctId和cooperatorId生成发票编码
     * 编码规则：根据rctId的总数量决定第一个字母(A-Z)，根据cooperatorId的数量决定后面的数字
     *
     * @param rctId        RCT主键
     * @param cooperatorId 合作方ID
     * @return 生成的编码字符串
     */
    @Override
    public String generateInvoiceCode(Long rctId, Long cooperatorId) {
        // 参数验证
        if (rctId == null || cooperatorId == null) {
            throw new IllegalArgumentException("rctId和cooperatorId不能为空");
        }

        if (rctId <= 0 || cooperatorId <= 0) {
            throw new IllegalArgumentException("rctId和cooperatorId必须大于0");
        }

        log.debug("开始生成发票编码，rctId: {}, cooperatorId: {}", rctId, cooperatorId);

        // 使用锁确保并发安全
        codeGenerationLock.lock();
        try {
            // 一次性查询获取两个计数
            Map<String, Object> counts = rsVatInvoiceMapper.countBothInvoices(rctId, cooperatorId);
            log.debug("查询返回的Map: {}", counts);

            Integer totalCount = (Integer) counts.get("totalCount");
            Integer cooperatorCount = (Integer) counts.get("cooperatorCount");
            String rctNo = (String) counts.get("rctNo");

            // 处理可能的空值
            if (totalCount == null) {
                log.warn("totalCount为null，使用默认值0");
                totalCount = 0;
            }

            if (cooperatorCount == null) {
                log.warn("cooperatorCount为null，使用默认值0");
                cooperatorCount = 0;
            }

            log.debug("查询结果 - rctId: {} 的总发票数量: {}, cooperatorId: {} 的发票数量: {}",
                    rctId, totalCount, cooperatorId, cooperatorCount);

            String firstLetter = "A";

            if (counts.containsKey("cooperatorInvoiceCodeNo")) {
                // 提取合作方发票编号首字母(RCT2507068-B1)中的B
                String cooperatorInvoiceCodeNo = (String) counts.get("cooperatorInvoiceCodeNo");
                firstLetter = cooperatorInvoiceCodeNo.split("-")[1].substring(0, 1);
            } else {
                // 生成第一个字母 (A-Z, 支持1-26)
                firstLetter = generateFirstLetter(totalCount);
            }


            // 生成合作方序号 (从1开始)
            int sequenceNumber = cooperatorCount;

            String invoiceCode = rctNo + "-" + firstLetter + sequenceNumber;
            log.info("成功生成发票编码: {} (rctId: {}, cooperatorId: {})", invoiceCode, rctId, cooperatorId);

            return invoiceCode;

        } catch (Exception e) {
            // 记录错误日志
            log.error("生成发票编码失败，rctId: {}, cooperatorId: {}, 错误: {}", rctId, cooperatorId, e.getMessage(), e);
            throw new RuntimeException("生成发票编码失败: " + e.getMessage(), e);
        } finally {
            codeGenerationLock.unlock();
        }
    }

    /**
     * 根据总数生成第一个字母
     *
     * @param totalCount 总数
     * @return 对应的字母
     */
    private String generateFirstLetter(int totalCount) {
        // 处理边界情况
        if (totalCount <= 0) {
            log.warn("发票总数为0或负数，使用默认字母A");
            return "A";
        }

        // 如果总数在1-26范围内，直接转换为对应字母
        if (totalCount <= 26) {
            char letter = (char) ('A' + totalCount - 1);
            return String.valueOf(letter);
        }

        // 如果超过26，使用Z表示
        log.warn("发票总数超过26个，使用字母Z表示，总数: {}", totalCount);
        return "Z";
    }

    /**
     * 获取RCT发票数量（带缓存）
     *
     * @param rctId RCT主键
     * @return 发票数量
     */
    private int getRctInvoiceCount(Long rctId) {
        return rctCountCache.computeIfAbsent(rctId, key -> {
            int count = rsVatInvoiceMapper.countRsVatInvoiceByRctId(key);
            log.debug("从数据库查询RCT发票数量，rctId: {}, count: {}", key, count);
            return count;
        });
    }

    /**
     * 获取合作方发票数量（带缓存）
     *
     * @param rctId        RCT主键
     * @param cooperatorId 合作方ID
     * @return 发票数量
     */
    private int getCooperatorInvoiceCount(Long rctId, Long cooperatorId) {
        String cacheKey = rctId + "_" + cooperatorId;
        return cooperatorCountCache.computeIfAbsent(cacheKey, key -> {
            int count = rsVatInvoiceMapper.countRsVatInvoiceByRctIdAndCooperatorId(rctId, cooperatorId);
            log.debug("从数据库查询合作方发票数量，rctId: {}, cooperatorId: {}, count: {}", rctId, cooperatorId, count);
            return count;
        });
    }

    /**
     * 清除缓存（在发票新增、删除、修改时调用）
     */
    public void clearCache() {
        rctCountCache.clear();
        cooperatorCountCache.clear();
        log.debug("已清除发票编码缓存");
    }

    /**
     * 写入发票数据到Excel工作簿
     *
     * @param invoiceIds 发票ID列表
     * @param workbook   工作簿对象
     */
    @Override
    public void writeInvoiceData(List<Long> invoiceIds, Workbook workbook) {
        if (invoiceIds == null || invoiceIds.isEmpty()) {
            throw new RuntimeException("发票ID列表不能为空");
        }

        Sheet invoice = workbook.getSheetAt(0);
        Sheet charge = workbook.getSheetAt(5);

        // 批量查询所有发票信息，避免N+1查询问题
        List<RsVatInvoice> rsVatInvoices = rsVatInvoiceMapper.selectRsVatInvoiceByInvoiceIds(invoiceIds);

        // 验证所有发票是否存在
        if (rsVatInvoices.size() != invoiceIds.size()) {
            Set<Long> foundIds = rsVatInvoices.stream()
                    .map(RsVatInvoice::getInvoiceId)
                    .collect(Collectors.toSet());
            List<Long> missingIds = invoiceIds.stream()
                    .filter(id -> !foundIds.contains(id))
                    .collect(Collectors.toList());
            throw new RuntimeException("以下发票不存在: " + missingIds);
        }

        int invoiceRowIndex = 3;
        int chargeRowIndex = 3; // 从第3行开始填充费用明细

        for (RsVatInvoice rsVatInvoice : rsVatInvoices) {
            // 填充发票基本信息
            writeInvoiceBasicInfo(rsVatInvoice, invoice, invoiceRowIndex);
            invoiceRowIndex += 1; // 每个发票占用10行

            // 获取并填充费用明细
            if (rsVatInvoice.getRsChargeList() != null && !rsVatInvoice.getRsChargeList().isEmpty()) {
                chargeRowIndex = writeChargeDetails(rsVatInvoice, charge, chargeRowIndex);
            }
        }
    }

    /**
     * 填充发票基本信息
     *
     * @param rsVatInvoice 发票对象
     * @param sheet        工作表对象
     * @param startRow     起始行索引
     */
    private void writeInvoiceBasicInfo(RsVatInvoice rsVatInvoice, Sheet sheet, int startRow) {
        // 发票流水号
        setCellValue(sheet, startRow, 0, rsVatInvoice.getInvoiceCodeNo());

        // 发票类型
        setCellValue(sheet, startRow, 1, rsVatInvoice.getInvoiceType());

        // 是否含税
        boolean hasTax = rsVatInvoice.getRsChargeList() != null &&
                rsVatInvoice.getRsChargeList().stream()
                        .anyMatch(charge -> charge.getDutyRate() != null && charge.getDutyRate().compareTo(BigDecimal.ZERO) != 0);
        setCellValue(sheet, startRow, 3, hasTax ? "是" : "否");

        // 购买方名称
        setCellValue(sheet, startRow, 5, rsVatInvoice.getCooperatorCompanyTitle());

        // 购买方纳税人识别号
        setCellValue(sheet, startRow, 6, rsVatInvoice.getCooperatorVatSerialNo());

        // 美金不用开户行和银行账号
        if (rsVatInvoice.getSettlementCurrencyCode().equals("USD")) {
            setCellValue(sheet, startRow, 22, "");
            setCellValue(sheet, startRow, 23, "");

            // 备注
            setCellValue(sheet, startRow, 17, "仅用于美元结算USD******；提单号：******\n" +
                    "公司名称：广州瑞旗国际货运代理有限公司\n" +
                    "开户行名称：招商银行股份有限公司广州体育东路支行\n" +
                    "美元账号: 1209 2526 0132 001\n" + rsVatInvoice.getInvoiceRemark());
        } else {
            // 销售方开户行
            setCellValue(sheet, startRow, 22, rsVatInvoice.getRichBankFullname());

            // 销售方银行账号
            setCellValue(sheet, startRow, 23, rsVatInvoice.getRichBankAccount());

            // 是否展示销售方
            setCellValue(sheet, startRow, 24, "展示开户银行、银行账号");

            // 备注
            setCellValue(sheet, startRow, 17, rsVatInvoice.getInvoiceRemark());
        }
    }

    /**
     * 填充费用明细
     *
     * @param sheet    工作表对象
     * @param startRow 起始行索引
     * @return 下一个可用的行索引
     */
    private int writeChargeDetails(RsVatInvoice rsVatInvoice, Sheet sheet, int startRow) {
        // 根据发票ID查询费用明细
        // 通过RsVatInvoiceMapper的查询，发票已经包含了关联的费用信息
        if (rsVatInvoice.getRsChargeList() == null || rsVatInvoice.getRsChargeList().isEmpty()) {
            return startRow;
        }

        int rowIndex = startRow;

        // 如果rsVatInvoice.getRsChargeList费用开票项目名称相同合并为一条记录且金额相加
        Map<String, RsCharge> chargeMap = new HashMap<>();
        for (RsCharge charge : rsVatInvoice.getRsChargeList()) {
            String invoicingItem = charge.getInvoicingItem();
            if (chargeMap.containsKey(invoicingItem)) {
                // 如果已存在相同项目名称，则合并金额
                RsCharge existingCharge = chargeMap.get(invoicingItem);
                // 合并数量
                existingCharge.setDnAmount(existingCharge.getDnAmount().add(charge.getDnAmount()));
                // 合并小计金额
                existingCharge.setSubtotal(existingCharge.getSubtotal().add(charge.getSubtotal()));
                // 如果税率不同，使用第一个非零税率，如果都为零则保持为零
                if (existingCharge.getDutyRate().compareTo(BigDecimal.ZERO) == 0 &&
                        charge.getDutyRate().compareTo(BigDecimal.ZERO) != 0) {
                    existingCharge.setDutyRate(charge.getDutyRate());
                }
            } else {
                // 如果不存在，则添加到Map中
                chargeMap.put(invoicingItem, charge);
            }
        }

        // 使用合并后的费用记录写入Excel
        for (RsCharge charge : chargeMap.values()) {
            Row row = getOrCreateRow(sheet, rowIndex);

            // 发票流水号
            setCellValue(row, 0, rsVatInvoice.getInvoiceCodeNo());

            // 项目名称
            setCellValue(row, 1, charge.getInvoicingItem());

            // 商品和服务税收编码
            setCellValue(row, 2, "3040802010200000000");


            // 单位
            setCellValue(row, 4, "票");

            // 数量（合并后的数量）
            setCellValue(row, 5, "1");

            // 单价和金额要乘以汇率
            // 单价,保留两位小数
            setCellValue(row, 6, charge.getSubtotal().multiply(rsVatInvoice.getInvoiceExchangeRate()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());

            // 金额（合并后的金额）
            setCellValue(row, 7, charge.getSubtotal().multiply(rsVatInvoice.getInvoiceExchangeRate()).setScale(2, BigDecimal.ROUND_HALF_UP).toString());

            // 税率,如果0.00则显示0
            if (charge.getDutyRate().compareTo(BigDecimal.ZERO) == 0) {
                setCellValue(row, 8, "0");
            } else {
                setCellValue(row, 8, charge.getDutyRate().divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP).toString());
            }

            if (charge.getDutyRate().compareTo(BigDecimal.ZERO) == 0) {
                // 是否使用优惠政策
                setCellValue(row, 10, "是");

                // 优惠政策类型
                setCellValue(row, 11, "免税");
            }


            rowIndex++;
        }

        return rowIndex;
    }

    /**
     * 设置单元格值
     *
     * @param sheet    工作表
     * @param rowIndex 行索引
     * @param colIndex 列索引
     * @param value    值
     */
    private void setCellValue(Sheet sheet, int rowIndex, int colIndex, String value) {
        Row row = getOrCreateRow(sheet, rowIndex);
        Cell cell = getOrCreateCell(row, colIndex);
        cell.setCellValue(value != null ? value : "");
    }

    /**
     * 设置单元格值
     *
     * @param row   行对象
     * @param col   列索引
     * @param value 值
     */
    private void setCellValue(Row row, int col, String value) {
        Cell cell = getOrCreateCell(row, col);
        cell.setCellValue(value != null ? value : "");
    }

    /**
     * 获取或创建工作表行
     *
     * @param sheet    工作表
     * @param rowIndex 行索引
     * @return 行对象
     */
    private Row getOrCreateRow(Sheet sheet, int rowIndex) {
        Row row = sheet.getRow(rowIndex);
        if (row == null) {
            row = sheet.createRow(rowIndex);
        }
        return row;
    }

    /**
     * 获取或创建单元格
     *
     * @param row 行对象
     * @param col 列索引
     * @return 单元格对象
     */
    private Cell getOrCreateCell(Row row, int col) {
        Cell cell = row.getCell(col);
        if (cell == null) {
            cell = row.createCell(col);
        }
        return cell;
    }

    /**
     * 合并发票
     * 选择第一条发票作为合并发票，删除其他发票，更新相关账单和费用记录
     *
     * @param invoiceIds 需要合并的发票ID列表
     * @return 合并后的发票对象
     */
    @Override
    @Transactional
    public RsVatInvoice mergeInvoices(List<Long> invoiceIds) {
        if (invoiceIds == null || invoiceIds.size() < 2) {
            throw new IllegalArgumentException("至少需要2张发票才能进行合并");
        }

        // 查询所有需要合并的发票
        List<RsVatInvoice> invoices = rsVatInvoiceMapper.selectRsVatInvoiceByInvoiceIds(invoiceIds);
        if (invoices.size() != invoiceIds.size()) {
            throw new IllegalArgumentException("部分发票不存在或已被删除");
        }

        // 验证发票是否可以合并
        validateInvoicesForMerge(invoices);

        // 选择第一条发票作为合并发票
        RsVatInvoice mergedInvoice = invoices.get(0);
        List<Long> otherInvoiceIds = invoiceIds.subList(1, invoiceIds.size());

        // 更新第一条发票的金额信息（合并所有发票的金额）
        updateMergedInvoiceAmounts(mergedInvoice, invoices);

        // 更新第一条发票的合并标记
        mergedInvoice.setMergeInvoice("1");
        mergedInvoice.setIsMerge("1");
//        mergedInvoice.setOpRemark("已合并其他发票: " + otherInvoiceIds);
        rsVatInvoiceMapper.updateRsVatInvoice(mergedInvoice);

        // 更新账单的belong_invoice_id为第一条发票的ID
        updateDebitNotesBelongInvoiceId(otherInvoiceIds, mergedInvoice.getInvoiceId());

        // 更新费用的发票ID和发票编码为第一条发票的ID和编码
        updateChargesInvoiceId(otherInvoiceIds, mergedInvoice.getInvoiceId(), mergedInvoice.getInvoiceCodeNo());

        // 删除其他发票
        deleteOtherInvoices(otherInvoiceIds);

        // 清除相关缓存
        clearCache();

        return mergedInvoice;
    }

    /**
     * 验证发票是否可以合并
     *
     * @param invoices 发票列表
     */
    private void validateInvoicesForMerge(List<RsVatInvoice> invoices) {
        if (invoices.isEmpty()) {
            throw new IllegalArgumentException("发票列表不能为空");
        }

        RsVatInvoice firstInvoice = invoices.get(0);

        for (RsVatInvoice invoice : invoices) {
            // 检查发票状态 - 只能合并未开票的发票
            if (!"unissued".equals(invoice.getInvoiceStatus()) && !"applied".equals(invoice.getInvoiceStatus())) {
                throw new IllegalArgumentException("只能合并状态为未开票或已申请的发票，发票ID: " + invoice.getInvoiceId());
            }

            // 检查进销项是否一致
            if (!firstInvoice.getSaleBuy().equals(invoice.getSaleBuy())) {
                throw new IllegalArgumentException("只能合并进销项一致的发票，发票ID: " + invoice.getInvoiceId());
            }

            // 检查对方公司是否一致
            if (!firstInvoice.getCooperatorId().equals(invoice.getCooperatorId())) {
                throw new IllegalArgumentException("只能合并对方公司一致的发票，发票ID: " + invoice.getInvoiceId());
            }

            // 检查币种是否一致
            if (!firstInvoice.getInvoiceCurrencyCode().equals(invoice.getInvoiceCurrencyCode())) {
                throw new IllegalArgumentException("只能合并币种一致的发票，发票ID: " + invoice.getInvoiceId());
            }
        }
    }





    /**
     * 更新合并发票的金额信息
     *
     * @param mergedInvoice 合并后的发票
     * @param invoices      原发票列表
     */
    private void updateMergedInvoiceAmounts(RsVatInvoice mergedInvoice, List<RsVatInvoice> invoices) {
        BigDecimal totalNetAmount = BigDecimal.ZERO;
        BigDecimal totalVatAmount = BigDecimal.ZERO;
        BigDecimal totalVatTotal = BigDecimal.ZERO;
        BigDecimal totalSaleNetSum = BigDecimal.ZERO;
        BigDecimal totalSaleTax = BigDecimal.ZERO;
        BigDecimal totalSaleTaxTotal = BigDecimal.ZERO;
        BigDecimal totalBuyNetSum = BigDecimal.ZERO;
        BigDecimal totalBuyTax = BigDecimal.ZERO;
        BigDecimal totalBuyTaxTotal = BigDecimal.ZERO;

        for (RsVatInvoice invoice : invoices) {
            totalNetAmount = totalNetAmount.add(invoice.getInvoiceNetAmount() != null ? invoice.getInvoiceNetAmount() : BigDecimal.ZERO);
            totalVatAmount = totalVatAmount.add(invoice.getVatAmount() != null ? invoice.getVatAmount() : BigDecimal.ZERO);
            totalVatTotal = totalVatTotal.add(invoice.getInvoiceVatAmount() != null ? invoice.getInvoiceVatAmount() : BigDecimal.ZERO);
            totalSaleNetSum = totalSaleNetSum.add(invoice.getSaleNetSum() != null ? invoice.getSaleNetSum() : BigDecimal.ZERO);
            totalSaleTax = totalSaleTax.add(invoice.getSaleTax() != null ? invoice.getSaleTax() : BigDecimal.ZERO);
            totalSaleTaxTotal = totalSaleTaxTotal.add(invoice.getSaleTaxTotal() != null ? invoice.getSaleTaxTotal() : BigDecimal.ZERO);
            totalBuyNetSum = totalBuyNetSum.add(invoice.getBuyNetSum() != null ? invoice.getBuyNetSum() : BigDecimal.ZERO);
            totalBuyTax = totalBuyTax.add(invoice.getBuyTax() != null ? invoice.getBuyTax() : BigDecimal.ZERO);
            totalBuyTaxTotal = totalBuyTaxTotal.add(invoice.getBuyTaxTotal() != null ? invoice.getBuyTaxTotal() : BigDecimal.ZERO);
        }

        mergedInvoice.setInvoiceNetAmount(totalNetAmount);
        mergedInvoice.setVatAmount(totalVatAmount);
        mergedInvoice.setInvoiceVatAmount(totalVatTotal);
        mergedInvoice.setSaleNetSum(totalSaleNetSum);
        mergedInvoice.setSaleTax(totalSaleTax);
        mergedInvoice.setSaleTaxTotal(totalSaleTaxTotal);
        mergedInvoice.setBuyNetSum(totalBuyNetSum);
        mergedInvoice.setBuyTax(totalBuyTax);
        mergedInvoice.setBuyTaxTotal(totalBuyTaxTotal);
    }

    /**
     * 更新账单的belong_invoice_id为第一条发票的ID
     *
     * @param invoiceIds 需要更新的发票ID列表
     * @param mergedInvoiceId 合并后的发票ID
     */
    private void updateDebitNotesBelongInvoiceId(List<Long> invoiceIds, Long mergedInvoiceId) {
        rsDebitNoteMapper.updateBelongInvoiceIdByInvoiceIds(invoiceIds, mergedInvoiceId);
    }

    /**
     * 更新费用的发票ID和发票编码为第一条发票的ID和编码
     *
     * @param invoiceIds 需要更新的发票ID列表
     * @param mergedInvoiceId 合并后的发票ID
     * @param mergedInvoiceCodeNo 合并后的发票编码
     */
    private void updateChargesInvoiceId(List<Long> invoiceIds, Long mergedInvoiceId, String mergedInvoiceCodeNo) {
        rsChargeMapper.updateSqdInvoiceIdByInvoiceIds(invoiceIds, mergedInvoiceId, mergedInvoiceCodeNo);
    }

    /**
     * 删除其他发票
     *
     * @param invoiceIds 需要删除的发票ID列表
     */
    private void deleteOtherInvoices(List<Long> invoiceIds) {
        for (Long invoiceId : invoiceIds) {
            rsVatInvoiceMapper.deleteRsVatInvoiceByInvoiceId(invoiceId);
        }
    }

    /**
     * 获取发票申请通知数量
     * 统计状态为"已申请"的发票数量，用于badge显示
     *
     * @param userId 用户ID，如果为null则获取全局数量
     * @return 发票申请通知数量
     */
    @Override
    public int invoiceNotification(Long userId) {
        try {
            RsVatInvoice queryInvoice = new RsVatInvoice();

            // 设置查询条件：状态为"已申请"的发票
            queryInvoice.setInvoiceStatus("applied");

            // 如果指定了用户ID，则只查询该用户相关的发票
            /*f (userId != null) {
                queryInvoice.setApplicantId(userId);
            }*/

            // 查询符合条件的发票列表
            List<RsVatInvoice> invoiceList = rsVatInvoiceMapper.selectRsVatInvoiceList(queryInvoice);

            // 返回数量
            return invoiceList != null ? invoiceList.size() : 0;

        } catch (Exception e) {
            log.error("获取发票申请通知数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }
}
