<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="showLeft">
        <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="query"
                 label-width="68px"
                 size="mini"
        >
          <el-form-item label="发票流水号" prop="invoiceCodeNo">
            <el-input
              v-model="queryParams.invoiceCodeNo"
              clearable
              placeholder="发票流水号"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="发票号码" prop="invoiceOfficalNo">
            <el-input
              v-model="queryParams.invoiceOfficalNo"
              clearable
              placeholder="发票号码"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="所属公司" prop="invoiceBelongsTo">
            <el-input
              v-model="queryParams.invoiceBelongsTo"
              clearable
              placeholder="所属公司"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="我司账户简称" prop="richBankCode">
            <el-input
              v-model="queryParams.richBankCode"
              clearable
              placeholder="我司账户简称"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="我司发票抬头" prop="richCompanyTitle">
            <el-input
              v-model="queryParams.richCompanyTitle"
              clearable
              placeholder="我司发票抬头"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="我司纳税人识别号" prop="richVatSerialNo">
            <el-input
              v-model="queryParams.richVatSerialNo"
              clearable
              placeholder="我司纳税人识别号"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="我司账号" prop="richBankAccount">
            <el-input
              v-model="queryParams.richBankAccount"
              clearable
              placeholder="我司账号"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="我司银行全称" prop="richBankFullname">
            <el-input
              v-model="queryParams.richBankFullname"
              clearable
              placeholder="我司银行全称"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方公司ID" prop="cooperatorId">
            <el-input
              v-model="queryParams.cooperatorId"
              clearable
              placeholder="对方公司ID"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方公司简称" prop="cooperatorShortName">
            <el-input
              v-model="queryParams.cooperatorShortName"
              clearable
              placeholder="对方公司简称"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方账户简称" prop="cooperatorBankCode">
            <el-input
              v-model="queryParams.cooperatorBankCode"
              clearable
              placeholder="对方账户简称"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方发票抬头" prop="cooperatorCompanyTitle">
            <el-input
              v-model="queryParams.cooperatorCompanyTitle"
              clearable
              placeholder="对方发票抬头"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方纳税人识别号" prop="cooperatorVatSerialNo">
            <el-input
              v-model="queryParams.cooperatorVatSerialNo"
              clearable
              placeholder="对方纳税人识别号"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方账号" prop="cooperatorBankAccount">
            <el-input
              v-model="queryParams.cooperatorBankAccount"
              clearable
              placeholder="对方账号"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方银行全称" prop="cooperatorBankFullname">
            <el-input
              v-model="queryParams.cooperatorBankFullname"
              clearable
              placeholder="对方银行全称"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="结算币种" prop="chargeCurrencyCode">
            <el-input
              v-model="queryParams.chargeCurrencyCode"
              clearable
              placeholder="结算币种"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="账单应收总额" prop="dnSum">
            <el-input
              v-model="queryParams.dnSum"
              clearable
              placeholder="账单应收总额"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="银行已收" prop="dnRecieved">
            <el-input
              v-model="queryParams.dnRecieved"
              clearable
              placeholder="银行已收"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="银行未收" prop="dnBalance">
            <el-input
              v-model="queryParams.dnBalance"
              clearable
              placeholder="银行未收"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="账单应付总额" prop="cnSum">
            <el-input
              v-model="queryParams.cnSum"
              clearable
              placeholder="账单应付总额"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="银行已付" prop="cnPaid">
            <el-input
              v-model="queryParams.cnPaid"
              clearable
              placeholder="银行已付"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="银行未付" prop="cnBalance">
            <el-input
              v-model="queryParams.cnBalance"
              clearable
              placeholder="银行未付"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="期望支付日期" prop="expectedPayDate">
            <el-date-picker v-model="queryParams.expectedPayDate"
                            clearable
                            placeholder="期望支付日期"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="批复支付日期" prop="approvedPayDate">
            <el-date-picker v-model="queryParams.approvedPayDate"
                            clearable
                            placeholder="批复支付日期"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="实际支付日期" prop="actualPayDate">
            <el-date-picker v-model="queryParams.actualPayDate"
                            clearable
                            placeholder="实际支付日期"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="发票币种" prop="invoiceCurrencyCode">
            <el-input
              v-model="queryParams.invoiceCurrencyCode"
              clearable
              placeholder="发票币种"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="发票汇率" prop="invoiceExchangeRate">
            <el-input
              v-model="queryParams.invoiceExchangeRate"
              clearable
              placeholder="发票汇率"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="不含税金额" prop="invoiceNetAmount">
            <el-input
              v-model="queryParams.invoiceNetAmount"
              clearable
              placeholder="不含税金额"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="税金" prop="vatAmount">
            <el-input
              v-model="queryParams.vatAmount"
              clearable
              placeholder="税金"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="价税合计" prop="invoiceVatAmount">
            <el-input
              v-model="queryParams.invoiceVatAmount"
              clearable
              placeholder="价税合计"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="销项不含税" prop="saleNetSum">
            <el-input
              v-model="queryParams.saleNetSum"
              clearable
              placeholder="销项不含税"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="销项税金" prop="saleTax">
            <el-input
              v-model="queryParams.saleTax"
              clearable
              placeholder="销项税金"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="销项含税合计" prop="saleTaxTotal">
            <el-input
              v-model="queryParams.saleTaxTotal"
              clearable
              placeholder="销项含税合计"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="进项不含税" prop="buyNetSum">
            <el-input
              v-model="queryParams.buyNetSum"
              clearable
              placeholder="进项不含税"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="进项税金" prop="buyTax">
            <el-input
              v-model="queryParams.buyTax"
              clearable
              placeholder="进项税金"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="进项含税合计" prop="buyTaxTotal">
            <el-input
              v-model="queryParams.buyTaxTotal"
              clearable
              placeholder="进项含税合计"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="发票性质" prop="taxClass">
            <el-input
              v-model="queryParams.taxClass"
              clearable
              placeholder="发票性质"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="报税所属月份" prop="belongsToMonth">
            <el-input
              v-model="queryParams.belongsToMonth"
              clearable
              placeholder="报税所属月份"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="申请人ID" prop="applyStuffId">
            <el-input
              v-model="queryParams.applyStuffId"
              clearable
              placeholder="申请人ID"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="申请时间" prop="appliedTime">
            <el-date-picker v-model="queryParams.appliedTime"
                            clearable
                            placeholder="申请时间"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="开票人ID" prop="issuedStuffId">
            <el-input
              v-model="queryParams.issuedStuffId"
              clearable
              placeholder="开票人ID"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="开票时间" prop="issuedTime">
            <el-date-picker v-model="queryParams.issuedTime"
                            clearable
                            placeholder="开票时间"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="报税人ID" prop="taxStuffId">
            <el-input
              v-model="queryParams.taxStuffId"
              clearable
              placeholder="报税人ID"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="报税时间" prop="taxDeclareTime">
            <el-date-picker v-model="queryParams.taxDeclareTime"
                            clearable
                            placeholder="报税时间"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="showRight">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:vatinvoice:add']"
              icon="el-icon-plus"
              plain
              size="mini"
              type="primary"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:vatinvoice:edit']"
              :disabled="single"
              icon="el-icon-edit"
              plain
              size="mini"
              type="success"
              @click="handleUpdate"
            >修改
            </el-button>
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:vatinvoice:remove']"
              :disabled="multiple"
              icon="el-icon-delete"
              plain
              size="mini"
              type="danger"
              @click="handleDelete"
            >删除
            </el-button>
          </el-col> -->
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:vatinvoice:export']"
              icon="el-icon-download"
              plain
              size="mini"
              type="warning"
              @click="handleExport"
            >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:vatinvoice:export']"
              icon="el-icon-download"
              plain
              size="mini"
              type="warning"
              @click="handleExportTheInvoicingInformation"
            >导出开票资料
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:vatinvoice:add']"
              :disabled="multiple"
              icon="el-icon-connection"
              plain
              size="mini"
              type="info"
              @click="handleMergeInvoices"
            >合并发票
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="vatinvoiceList" @selection-change="handleSelectionChange"
                  @row-dblclick="handleRowClick"
        >
          <el-table-column align="center" type="selection" :selectable="selectableRow" width="28"/>
          <el-table-column align="center" label="发票流水号" prop="invoiceCodeNo" width="120"/>
          <el-table-column align="center" label="发票号码" prop="invoiceOfficalNo" width="120">
            <template #default="scope">
              {{ scope.row.invoiceStatus }} {{ scope.row.invoiceOfficalNo }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="所属公司" prop="invoiceBelongsTo" width="50"/>
          <el-table-column align="center" label="进销项" prop="saleBuy" width="50">
            <template #default="scope">
              {{ scope.row.saleBuy == "sale" ? "+" : "-" }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="发票性质" prop="taxClass" show-overflow-tooltip width="60"/>
          <el-table-column align="center" label="发票类型" prop="invoiceType" show-overflow-tooltip width="60"/>
          <el-table-column align="center" label="对方发票抬头" prop="cooperatorCompanyTitle" show-overflow-tooltip
                           width="80"
          />
          <el-table-column align="center" label="结算金额" prop="settlementAmount" show-overflow-tooltip width="80"/>
          <el-table-column align="center" label="发票币种" prop="invoiceCurrencyCode" width="60"/>
          <el-table-column align="center" label="发票汇率" prop="invoiceExchangeRate" width="60"/>
          <el-table-column align="right" label="发票金额" prop="invoiceAmount" width="180">
            <template #default="scope">
              {{
                currency(scope.row.invoiceNetAmount).format({symbol: "", decimal: ".", precision: 2})
              }}+{{
                currency(scope.row.vatAmount).format({symbol: "", decimal: ".", precision: 2})
              }}={{ currency(scope.row.invoiceVatAmount).format({symbol: "", decimal: ".", precision: 2}) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="开票要求" prop="opRemark" show-overflow-tooltip width="120"/>
          <el-table-column align="center" label="期望支付" prop="expectedPayDate" show-overflow-tooltip width="60">
            <template #default="scope">
              <span>{{ scope.row.expectedPayDate ? moment(scope.row.expectedPayDate).format("YYMMDD") : "" }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="批复支付" prop="approvedPayDate" show-overflow-tooltip width="60">
            <template #default="scope">
              <span>{{ scope.row.approvedPayDate ? moment(scope.row.approvedPayDate).format("YYMMDD") : "" }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="实际支付" prop="actualPayDate" show-overflow-tooltip width="60">
            <template #default="scope">
              <span>{{ scope.row.actualPayDate ? moment(scope.row.actualPayDate).format("YYMMDD") : "" }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="计税月份" prop="belongsToMonth" show-overflow-tooltip width="80"/>
          <el-table-column align="center" label="申请人" prop="applyStuffId" show-overflow-tooltip>
            <template #default="scope">
              <span>{{
                  getName(scope.row.applyStuffId)
                }}-{{ scope.row.appliedTime ? moment(scope.row.appliedTime).format("YYMMDD") : "" }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="审核人" prop="auditStuffId" show-overflow-tooltip>
            <template #default="scope">
              <span>{{
                  getName(scope.row.auditStuffId)
                }}-{{ scope.row.auditTime ? moment(scope.row.auditTime).format("YYMMDD") : "" }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="开票人" prop="issuedStuffId" show-overflow-tooltip>
            <template #default="scope">
              <span>{{
                  getName(scope.row.issuedStuffId)
                }}-{{ scope.row.issuedTime ? moment(scope.row.issuedTime).format("YYMMDD") : "" }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="报税人" prop="taxStuffId" show-overflow-tooltip>
            <template #default="scope">
              <span>{{
                  getName(scope.row.taxStuffId)
                }}-{{ scope.row.taxDeclareTime ? moment(scope.row.taxDeclareTime).format("YYMMDD") : "" }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNum"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改发票登记对话框 -->
    <vatinvoice-dialog
      :form="form"
      :invoice-items="invoiceItemList"
      :bank-account-list="bankAccountList"
      :company-list="companyList"
      :rules="rules"
      :title="title"
      :visible.sync="open"
      @cancel="cancel"
      @submit="handleDialogSubmit"
      @auditInvoice="handleAuditInvoice"
      @sendInvoice="handleSendInvoice"
      @invoiceApplyCancel="handleInvoiceApplyCancel"
      @invoiceAuditCancel="handleInvoiceAuditCancel"
    />
  </div>
</template>

<script>
import {
  addVatinvoice,
  changeStatus,
  delVatinvoice,
  getVatinvoice,
  listVatinvoice,
  updateVatinvoice,
  generateInvoiceExcel,
  mergeInvoices, generateInvoiceCode
} from "@/api/system/vatInvoice"
import store from "@/store"
import VatinvoiceDialog from "./components/VatinvoiceDialog"
import {updateDebitNoteByInvoiceId} from "@/api/system/debitnote"
import {parseTime} from "../../../utils/rich"
import moment from "moment"
import currency from "currency.js"

export default {
  name: "Vatinvoice",
  computed: {
    moment() {
      return moment
    }
  },
  components: {
    VatinvoiceDialog
  },
  data() {
    return {
      showLeft: 0,
      showRight: 24,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 发票登记表格数据
      vatinvoiceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        invoiceCodeNo: null,
        invoiceOfficalNo: null,
        saleBuy: null,
        invoiceBelongsTo: null,
        richBankCode: null,
        richCompanyTitle: null,
        richVatSerialNo: null,
        richBankAccount: null,
        richBankFullname: null,
        cooperatorId: null,
        cooperatorShortName: null,
        cooperatorBankCode: null,
        cooperatorFullname: null,
        cooperatorVatSerialNo: null,
        cooperatorBankAccount: null,
        cooperatorBankFullname: null,
        rctNoSummary: null,
        cooperatorReferNo: null,
        officalChargeNameSummary: null,
        chargeCurrencyCode: null,
        dnSum: null,
        dnRecieved: null,
        dnBalance: null,
        cnSum: null,
        cnPaid: null,
        cnBalance: null,
        chargeClearStatus: null,
        expectedPayDate: null,
        approvedPayDate: null,
        actualPayDate: null,
        sqdBankStatementList: null,
        invoiceCurrencyCode: null,
        invoiceExchangeRate: null,
        invoiceNetAmount: null,
        vatAmount: null,
        invoiceVatAmount: null,
        saleNetSum: null,
        saleTax: null,
        saleTaxTotal: null,
        buyNetSum: null,
        buyTax: null,
        buyTaxTotal: null,
        taxClass: null,
        invoiceType: null,
        belongsToMonth: null,
        invoiceStatus: null,
        invoiceRemark: null,
        applyStuffId: null,
        appliedTime: null,
        issuedStuffId: null,
        issuedTime: null,
        taxStuffId: null,
        taxDeclareTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        invoiceCodeNo: [
          {
            required: true,
            message: "发票流水号不能为空",
            trigger: "blur"
          }
        ],
        saleBuy: [
          {
            required: true,
            message: "进销标志：sale=销项，buy=进项不能为空",
            trigger: "blur"
          }
        ]
      },
      invoiceItemList: [],
      companyList: [],
      bankAccountList: []
    }
  },
  watch: {
    showSearch(n) {
      if (n === true) {
        this.showRight = 21
        this.showLeft = 3
      } else {
        this.showRight = 24
        this.showLeft = 0
      }
    }
  },
  created() {
    this.getList()
    this.loadStaff()
  },
  methods: {
    selectableRow(row) {
      return row.auditStuffId !== null
    },
    async handleInvoiceAuditCancel(formData) {
      // 修改发票
      const response = await updateVatinvoice(formData)
      // 更新分账单
      const updateData = {
        invoiceId: formData.invoiceId,
        invoiceStatus: "unissued"
      }
      const updateResponse = await updateDebitNoteByInvoiceId(updateData)
      if (updateResponse.code === 200 && response.code === 200) {
        this.$message.success("取消审核成功")
        this.getList()
        this.open = false
      } else {
        this.$message.error("取消审核失败")
      }
    },
    async handleInvoiceApplyCancel(formData) {
      // 修改发票
      const response = await updateVatinvoice(formData)
      // 更新分账单
      const updateData = {
        invoiceId: formData.invoiceId,
        invoiceStatus: "unissued"
      }
      const updateResponse = await updateDebitNoteByInvoiceId(updateData)
      if (updateResponse.code === 200 && response.code === 200) {
        this.$message.success("取消申请成功")
        this.getList()
        this.open = false
      } else {
        this.$message.error("取消申请失败")
      }
    },
    currency,
    parseTime,
    handleRowClick(row) {
      this.handleUpdate(row)
    },
    /** 查询发票登记列表 */
    getList() {
      this.loading = true
      // 不显示未申请开票的发票
      listVatinvoice(this.queryParams).then(response => {
        this.vatinvoiceList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        invoiceId: null,
        invoiceCodeNo: null,
        invoiceOfficalNo: null,
        saleBuy: null,
        invoiceBelongsTo: null,
        richBankCode: null,
        richCompanyTitle: null,
        richVatSerialNo: null,
        richBankAccount: null,
        richBankFullname: null,
        cooperatorId: null,
        cooperatorShortName: null,
        cooperatorBankCode: null,
        cooperatorFullname: null,
        cooperatorVatSerialNo: null,
        cooperatorBankAccount: null,
        cooperatorBankFullname: null,
        rctNoSummary: null,
        cooperatorReferNo: null,
        officalChargeNameSummary: null,
        chargeCurrencyCode: null,
        dnSum: null,
        dnRecieved: null,
        dnBalance: null,
        cnSum: null,
        cnPaid: null,
        cnBalance: null,
        chargeClearStatus: "0",
        expectedPayDate: null,
        approvedPayDate: null,
        actualPayDate: null,
        sqdBankStatementList: null,
        invoiceCurrencyCode: null,
        invoiceExchangeRate: null,
        invoiceNetAmount: null,
        vatAmount: null,
        invoiceVatAmount: null,
        saleNetSum: null,
        saleTax: null,
        saleTaxTotal: null,
        buyNetSum: null,
        buyTax: null,
        buyTaxTotal: null,
        taxClass: null,
        invoiceType: null,
        belongsToMonth: null,
        invoiceStatus: "0",
        invoiceRemark: null,
        applyStuffId: null,
        appliedTime: null,
        issuedStuffId: null,
        issuedTime: null,
        taxStuffId: null,
        taxDeclareTime: null,
        createTime: null,
        updateTime: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用"
      this.$modal.confirm("确认要\"" + text + "吗？").then(function () {
        return changeStatus(row.invoiceId, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + "成功")
      }).catch(function () {
        row.status = row.status === "0" ? "1" : "0"
      })
    },
    // 判断行是否可选
    isSelectable(row) {
      // 发票状态为merged的行不可勾选
      return row.invoiceStatus !== "merged" && row.mergeInvoice === "1"
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.invoiceId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加发票登记"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const invoiceId = row.invoiceId || this.ids
      getVatinvoice(invoiceId).then(response => {
        this.form = response.data
        this.companyList = response.extCompanyList
        this.bankAccountList = response.basAccountList
        this.open = true
        this.title = "修改发票登记"
      })
    },
    /** 提交按钮 */
    submitForm() {
      // 已移至handleDialogSubmit方法
    },

    /** 处理对话框提交 */
    handleDialogSubmit(formData) {
      if (formData.invoiceId != null) {
        updateVatinvoice(formData).then(response => {
          this.$modal.msgSuccess("修改成功")
          this.open = false
          this.getList()
        })
      } else {
        addVatinvoice(formData).then(response => {
          this.$modal.msgSuccess("新增成功")
          this.open = false
          this.getList()
        })
      }
    },
    /** 处理审核发票事件 */
    async handleAuditInvoice(row) {
      let invoiceCodeNo
      if (row.rctId && row.cooperatorId) {
        invoiceCodeNo = await this.generateInvoiceCodeNo(row.rctId, row.cooperatorId)
      }
      row.invoiceCodeNo = invoiceCodeNo
      row.rsChargeList = row.rsChargeList.map(charge => {
        return {
          ...charge,
          sqdInvoiceCodeNo: invoiceCodeNo
        }
      })
      // 调用API更新发票审核信息
      const response = await updateVatinvoice(row)

      // 更新账单发票信息->已审核
      const response2 = await updateDebitNoteByInvoiceId({
        invoiceId: row.invoiceId,
        invoiceCodeNo: invoiceCodeNo,
        invoiceStatus: "confirmed"
      })

      if (response.code === 200 && response2.code === 200) {
        this.$message.success("信息审核成功")
        this.getList()
      }
    },
    // 生成发票流水号
    async generateInvoiceCodeNo(rctId, cooperatorId) {
      try {
        // 调用API生成发票编码
        const response = await generateInvoiceCode(rctId, cooperatorId)
        if (response.code === 200) {
          return response.msg
        }
      } catch (error) {
        console.error("生成发票编码失败:", error)
      }
      // 如果API调用失败，返回默认格式
      return ""
    },
    /** 处理发送开票事件 */
    handleSendInvoice(formData) {
      // 更新debitNote为已开票
      const invoiceId = formData.invoiceId
      if (invoiceId) {
        updateDebitNoteByInvoiceId({invoiceId, invoiceStatus: "issued"})
          .then(response => {
            if (response.code === 200) {
              this.$message.success("分账单发票状态更新成功")
            } else {
              this.$message.error(response.msg || "分账单发票状态更新失败")
            }
          })
          .catch(error => {
            console.error("分账单发票状态更新失败:", error)
            this.$message.error("分账单发票状态更新失败，请重试")
          })
      }

      // 刷新列表
      this.getList()
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const invoiceIds = row.invoiceId || this.ids
      this.$modal.confirm("是否确认删除发票登记编号为\"" + invoiceIds + "\"的数据项？").then(function () {
        return delVatinvoice(invoiceIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {
      })
    },
    /** 导出开票资料按钮操作 */
    handleExportTheInvoicingInformation() {
      // 检查是否有选中的发票
      if (this.ids.length === 0) {
        this.$modal.msgError("请先选择要导出的发票")
        return
      }

      generateInvoiceExcel(this.ids)
        .then(response => {
          // 获取文件的字节数组 (ArrayBuffer)
          const data = response

          // 生成文件名
          let fileName = `Invoices_${new Date().getTime()}.xlsx`

          // 创建一个 Blob 对象来存储文件
          const blob = new Blob([data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"  // Excel 文件类型
          })

          // 创建一个临时链接，模拟点击来下载文件
          const link = document.createElement("a")
          const url = window.URL.createObjectURL(blob)  // 创建一个 URL 指向 Blob 对象
          link.href = url
          link.download = fileName  // 设置下载的文件名

          // 模拟点击链接，触发下载
          document.body.appendChild(link)
          link.click()

          // 下载完成后移除链接，并释放 URL 对象
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)

          this.$modal.msgSuccess("Excel文件导出成功")
        })
        .catch(error => {
          console.error("文件下载失败:", error)
          this.$modal.msgError("Excel文件导出失败")
        })
    },
    /** 合并发票按钮操作 */
    handleMergeInvoices() {

      if (this.isSelectable) {
        this.$modal.msgError("选中的发票有不能合并的项目")
        return
      }

      // 检查是否有选中的发票
      if (this.ids.length < 2) {
        this.$modal.msgError("请至少选择2张发票进行合并")
        return
      }

      // 获取选中的发票数据
      const selectedInvoices = this.vatinvoiceList.filter(item => this.ids.includes(item.invoiceId))

      // 检查发票状态和类型是否一致
      const firstInvoice = selectedInvoices[0]
      const canMerge = selectedInvoices.every(invoice => {
        // 检查发票状态 - 只能合并未开票的发票
        if (invoice.invoiceStatus !== "unissued" && invoice.invoiceStatus !== "applied") {
          return false
        }
        // 检查进销项是否一致
        if (invoice.saleBuy !== firstInvoice.saleBuy) {
          return false
        }
        // 检查对方公司是否一致
        if (invoice.cooperatorId !== firstInvoice.cooperatorId) {
          return false
        }
        // 检查币种是否一致
        if (invoice.invoiceCurrencyCode !== firstInvoice.invoiceCurrencyCode) {
          return false
        }
        return true
      })

      if (!canMerge) {
        this.$modal.msgError("只能合并状态为未开票或已申请、进销项一致、对方公司一致、币种一致的发票")
        return
      }

      // 确认合并操作
      this.$modal.confirm(`确认要合并选中的 ${this.ids.length} 张发票吗？合并后将生成一张新的发票，原发票将被标记为已合并。`).then(() => {
        mergeInvoices(this.ids).then(response => {
          if (response.code === 200) {
            this.$modal.msgSuccess("发票合并成功")
            this.getList() // 刷新列表
          } else {
            this.$modal.msgError(response.msg || "发票合并失败")
          }
        }).catch(error => {
          console.error("发票合并失败:", error)
          this.$modal.msgError("发票合并失败，请重试")
        })
      }).catch(() => {
        // 用户取消操作
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download("system/vatinvoice/export", {
        ...this.queryParams
      }, `vatinvoice_${new Date().getTime()}.xlsx`)
    },
    getName(id) {
      if (id) {
        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]
        if (staff) {
          return staff.staffShortName
        } else {
          return ""
        }
      } else {
        return ""
      }
    },
    loadStaff() {
      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {
        store.dispatch("getAllRsStaffList").then(() => {
          this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === "业务部")
        })
      } else {
        this.staffList = this.$store.state.data.allRsStaffList.filter(item => item.dept.deptLocalName === "业务部")
      }
    }
  }
}
</script>
