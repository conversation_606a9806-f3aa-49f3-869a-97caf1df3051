// OP文档通用样式表
// 用于op.vue及其子服务组件


.rct-no {
  height: 32px;
  width: 100%;
  text-align: center;
  font-size: 22px;
  font-weight: 600;
}

.tableForm {
  .el-form-item__label {
    padding: 0;
    width: 100%;
    text-align: center;
  }

  .el-form-item__content {
    padding: 0;
  }
}

.count-box {
  display: flex;

  .count-title {
    font-size: 12px;
    background: white;
    border: 1px solid #dfe6ec;
    width: 25%;

  }

  .count-content {
    width: 80%;
    font-size: 12px;
    border: 1px solid #dfe6ec;
  }
}

.spc {
  ::v-deep.el-form-item__content {
    padding-left: 0;
  }
}

.unHighlight-text {
  color: #b7bbc2;
  font-size: 12px;
  margin: 0;
}


// 去掉弹框内容的默认padding值
.dialog {
  ::v-deep.el-dialog--center .el-dialog__body {
    padding: 0;
  }
}

.basic-font {
  font-size: 13px;
}

.drag-area {
  position: fixed;
  right: 20px;
  top: 20%;
  padding: 13px;
  width: fit-content;
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  box-shadow: 0px 2px 15px 0px rgba(9, 41, 77, 0.15);
  cursor: move;
  user-select: none;
  text-align: center;
  background-color: #FFFFFF;
}

.form-box {
  display: flex;
  border: 1px solid #DCDFE6 !important;
  width: 100%;
  margin-bottom: 3px;

  .form-item {
    flex: 2;
    width: 200px;
    height: 25px;
    line-height: 25px;
    font-size: 13px;
  }

  .form-content {
    flex: 4;
    line-height: 25px;
    font-size: 13px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
  }
}

.custom-form-label {
  height: 28px;
  width: 100%;
  font-size: 15px;
  font-weight: normal;
  -moz-text-align-last: left !important;
  text-align-last: left !important;
  line-height: 26px;
  padding-left: 2px !important;
  border-top: solid 1px #DCDFE6;
  border-left: solid 1px #DCDFE6;
  border-bottom: solid 1px #DCDFE6;
}

.select-label {
  height: 28px;
  font-size: 15px;
  font-weight: normal;
  -moz-text-align-last: left !important;
  text-align-last: left !important;
  line-height: 26px;
  padding-left: 2px !important;
  border-top: solid 1px #DCDFE6;
  border-left: solid 1px #DCDFE6;
  border-bottom: solid 1px #DCDFE6;
}

.select-btn-group {
  float: right;
}

/*::v-deep .el-checkbox-button__inner {
  background-color: rgb(255, 242, 204);
}*/
.service-bar {
  background-color: rgba(153, 191, 215, 0.57);
  border-radius: 5px;
  height: 30px;
  text-align: center;

  a {
    line-height: 30px;
  }

  h3 {
    line-height: 30px;
  }
}

/* 设置滚动条宽度 */
::-webkit-scrollbar {
  width: 20px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #909399;
  border-radius: 6px;
}

/* 鼠标悬停在滑块上时 */
::-webkit-scrollbar-thumb:hover {
  background: #606266;
}
