{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNodeList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNodeList.vue", "mtime": 1754960767990}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgY3VycmVuY3kgZnJvbSAiY3VycmVuY3kuanMiDQppbXBvcnQgZGViaXROb3RlQ2hhcmdlTGlzdCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9kb2N1bWVudC9kZWJpdE5vdGVDaGFyZ2VMaXN0LnZ1ZSINCmltcG9ydCBWYXRpbnZvaWNlRGlhbG9nIGZyb20gIkAvdmlld3Mvc3lzdGVtL3ZhdGludm9pY2UvY29tcG9uZW50cy9WYXRpbnZvaWNlRGlhbG9nLnZ1ZSINCmltcG9ydCB7DQogIGFkZFZhdGludm9pY2UsDQogIHVwZGF0ZVZhdGludm9pY2UsDQogIGdldFZhdGludm9pY2UsDQogIGNvdW50VmF0aW52b2ljZUJ5UmN0SWQsDQogIGdlbmVyYXRlSW52b2ljZUNvZGUNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL3ZhdEludm9pY2UiDQppbXBvcnQge3VwZGF0ZURlYml0Tm90ZX0gZnJvbSAiQC9hcGkvc3lzdGVtL2RlYml0bm90ZSINCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiZGViaXROb3RlTGlzdCIsDQogIGNvbXBvbmVudHM6IHtkZWJpdE5vdGVDaGFyZ2VMaXN0LCBWYXRpbnZvaWNlRGlhbG9nfSwNCiAgcHJvcHM6IFsNCiAgICAiY29tcGFueUxpc3QiLA0KICAgICJkaXNhYmxlZCIsDQogICAgImhpZGRlblN1cHBsaWVyIiwNCiAgICAicmN0SWQiLA0KICAgICJkZWJpdE5vdGVMaXN0IiwNCiAgICAiaXNSZWNlaXZhYmxlIg0KICBdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGV4cGFuZGVkUm93czogW10sDQogICAgICBsb2NhbERlYml0Tm90ZUxpc3Q6IFtdLA0KICAgICAgc2VsZWN0ZWREZWJpdE5vdGVzOiBbXSwNCiAgICAgIGludm9pY2VEaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGN1cnJlbnREZWJpdE5vdGU6IG51bGwsDQogICAgICBpbnZvaWNlRm9ybToge30sDQogICAgICBpbnZvaWNlSXRlbXM6IFtdDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgIGRlYml0Tm90ZUxpc3Q6IHsNCiAgICAgIGltbWVkaWF0ZTogdHJ1ZSwNCiAgICAgIGRlZXA6IHRydWUsDQogICAgICBoYW5kbGVyKG5ld1ZhbCkgew0KICAgICAgICB0aGlzLiRlbWl0KCJ1cGRhdGU6ZGViaXROb3RlTGlzdCIsIG5ld1ZhbCkNCiAgICAgICAgdGhpcy4kZW1pdCgicmV0dXJuIiwgbmV3VmFsKQ0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHt9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5aSE55CG6KGo5qC86YCJ5oup5Y+Y5YyWDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5zZWxlY3RlZERlYml0Tm90ZXMgPSBzZWxlY3Rpb24NCiAgICAgIHRoaXMuJGVtaXQoInNlbGVjdGlvbi1jaGFuZ2UiLCBzZWxlY3Rpb24pDQogICAgfSwNCiAgICBkZWJpdE5vdGVEaXNhYmxlZChyb3cpIHsNCiAgICAgIHJldHVybiByb3cuYmlsbFN0YXR1cyA9PT0gImNvbmZpcm1lZCIgfHwgdGhpcy5kaXNhYmxlZA0KICAgIH0sDQogICAgYXN5bmMgaGFuZGxlSW52b2ljZVN0YXR1c0NsaWNrKHJvdykgew0KICAgICAgLy8g55Sz6K+35byA56WoDQogICAgICB0aGlzLmN1cnJlbnREZWJpdE5vdGUgPSByb3cNCg0KICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJaW52b2ljZUlkDQogICAgICBpZiAocm93Lmludm9pY2VJZCkgew0KICAgICAgICB0cnkgew0KICAgICAgICAgIC8vIOagueaNrmludm9pY2VJZOafpeaJvueOsOacieWPkeelqOS/oeaBrw0KICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2V0VmF0aW52b2ljZShyb3cuaW52b2ljZUlkKQ0KICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09PSAyMDAgJiYgcmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgICAgLy8g5L2/55So546w5pyJ5Y+R56Wo5L+h5oGv5YeG5aSH5pWw5o2uDQogICAgICAgICAgICB0aGlzLnByZXBhcmVJbnZvaWNlRGF0YVdpdGhFeGlzdGluZyhyb3csIHJlc3BvbnNlLmRhdGEpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOafpeaJvuWksei0pe+8jOS9v+eUqOm7mOiupOaVsOaNrg0KICAgICAgICAgICAgYXdhaXQgdGhpcy5wcmVwYXJlSW52b2ljZURhdGEocm93KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5blj5Hnpajkv6Hmga/lpLHotKU6IiwgZXJyb3IpDQogICAgICAgICAgLy8g5Ye66ZSZ5pe25L2/55So6buY6K6k5pWw5o2uDQogICAgICAgICAgYXdhaXQgdGhpcy5wcmVwYXJlSW52b2ljZURhdGEocm93KQ0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDmsqHmnIlpbnZvaWNlSWTvvIzliJvlu7rmlrDnmoTlj5HnpagNCiAgICAgICAgYXdhaXQgdGhpcy5wcmVwYXJlSW52b2ljZURhdGEocm93KQ0KICAgICAgfQ0KDQogICAgICB0aGlzLmludm9pY2VEaWFsb2dWaXNpYmxlID0gdHJ1ZQ0KICAgIH0sDQoNCiAgICAvLyDnlJ/miJDlj5HnpajmtYHmsLTlj7cNCiAgICBhc3luYyBnZW5lcmF0ZUludm9pY2VDb2RlTm8ocmN0SWQsIGNvb3BlcmF0b3JJZCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgLy8g6LCD55SoQVBJ55Sf5oiQ5Y+R56Wo57yW56CBDQogICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZ2VuZXJhdGVJbnZvaWNlQ29kZShyY3RJZCwgY29vcGVyYXRvcklkKQ0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgcmV0dXJuIHJlc3BvbnNlLm1zZw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLnlJ/miJDlj5HnpajnvJbnoIHlpLHotKU6IiwgZXJyb3IpDQogICAgICB9DQogICAgICAvLyDlpoLmnpxBUEnosIPnlKjlpLHotKXvvIzov5Tlm57pu5jorqTmoLzlvI8NCiAgICAgIHJldHVybiAiIg0KICAgIH0sDQoNCiAgICAvLyDlh4blpIflj5Hnpajlr7nor53moYbmlbDmja7vvIjmlrDlu7rlj5HnpajvvIkNCiAgICBhc3luYyBwcmVwYXJlSW52b2ljZURhdGEoZGViaXROb3RlKSB7DQogICAgICAvLyDnlJ/miJDlj5HnpajmtYHmsLTlj7cNCiAgICAgIGxldCBpbnZvaWNlQ29kZU5vDQogICAgICBpZiAodGhpcy5yY3RJZCAmJiBkZWJpdE5vdGUuY2xlYXJpbmdDb21wYW55SWQpIHsNCiAgICAgICAgbGV0IGludm9pY2VDb2RlID0gYXdhaXQgdGhpcy5nZW5lcmF0ZUludm9pY2VDb2RlTm8odGhpcy5yY3RJZCwgZGViaXROb3RlLmNsZWFyaW5nQ29tcGFueUlkKQ0KICAgICAgICBpbnZvaWNlQ29kZU5vID0gZGViaXROb3RlLnNxZFJjdE5vICsgIi0iICsgaW52b2ljZUNvZGUNCiAgICAgIH0NCg0KICAgICAgLy8g6K6+572u5Y+R56Wo6KGo5Y2V5pWw5o2uDQogICAgICB0aGlzLmludm9pY2VGb3JtID0gew0KICAgICAgICAvLyDln7rmnKzlj5Hnpajkv6Hmga8NCiAgICAgICAgaW52b2ljZUlkOiBkZWJpdE5vdGUuaW52b2ljZUlkIHx8IG51bGwsIC8vIOWPkeelqElEDQogICAgICAgIGludm9pY2VDb2RlTm86IGludm9pY2VDb2RlTm8sDQogICAgICAgIHNhbGVCdXk6IGRlYml0Tm90ZS5pc1JlY2lldmluZ09yUGF5aW5nID09IDAgPyAic2FsZSIgOiAiYnV5IiwgLy8g5qC55o2u5pS25LuY5qCH5b+X6K6+572u6L+b6ZSA6aG5DQogICAgICAgIHRheENsYXNzOiAiIiwNCiAgICAgICAgaW52b2ljZVR5cGU6ICLlop7lgLznqI7lj5HnpagiLA0KICAgICAgICBtZXJnZUludm9pY2U6IGRlYml0Tm90ZS5tZXJnZUludm9pY2UgfHwgIjAiLA0KICAgICAgICBpbnZvaWNlT2ZmaWNhbE5vOiBkZWJpdE5vdGUuaW52b2ljZU9mZmljYWxObyB8fCAiIiwNCg0KICAgICAgICAvLyDlhazlj7jlkozotKbmiLfkv6Hmga8NCiAgICAgICAgaW52b2ljZUJlbG9uZ3NUbzogZGViaXROb3RlLmNvbXBhbnlCZWxvbmdzVG8gfHwgIiIsDQogICAgICAgIHJpY2hCYW5rQ29kZTogZGViaXROb3RlLmJhbmtBY2NvdW50Q29kZSB8fCAiIiwNCiAgICAgICAgY29vcGVyYXRvcklkOiBkZWJpdE5vdGUuY2xlYXJpbmdDb21wYW55SWQgfHwgIiIsDQogICAgICAgIGNvb3BlcmF0b3JCYW5rQ29kZTogZGViaXROb3RlLmNsZWFyaW5nQ29tcGFueUJhbmtBY2NvdW50IHx8ICIiLA0KICAgICAgICByaWNoQ29tcGFueVRpdGxlOiBkZWJpdE5vdGUuYmFua0FjY291bnROYW1lIHx8ICIiLA0KICAgICAgICBjb29wZXJhdG9yQ29tcGFueVRpdGxlOiBkZWJpdE5vdGUuY2xlYXJpbmdDb21wYW55TmFtZSB8fCAiIiwNCg0KICAgICAgICAvLyDpobnnm67lkozorqLljZXkv6Hmga8NCiAgICAgICAgb2ZmaWNhbENoYXJnZU5hbWVTdW1tYXJ5OiAiIiwNCiAgICAgICAgcmVsYXRlZE9yZGVyTm86ICIiLA0KDQogICAgICAgIC8vIOeojuWPt+WSjOmTtuihjOS/oeaBrw0KICAgICAgICByaWNoVmF0U2VyaWFsTm86ICIiLA0KICAgICAgICBjb29wZXJhdG9yVmF0U2VyaWFsTm86ICIiLA0KICAgICAgICByaWNoQmFua0Z1bGxuYW1lOiAiIiwNCiAgICAgICAgY29vcGVyYXRvckJhbmtGdWxsbmFtZTogIiIsDQogICAgICAgIHJpY2hCYW5rQWNjb3VudDogIiIsDQogICAgICAgIGNvb3BlcmF0b3JCYW5rQWNjb3VudDogZGViaXROb3RlLmNsZWFyaW5nQ29tcGFueUJhbmtBY2NvdW50IHx8ICIiLA0KDQogICAgICAgIC8vIOWkh+azqOWSjOaXpeacn+S/oeaBrw0KICAgICAgICBpbnZvaWNlUmVtYXJrOiAiIiwNCiAgICAgICAgZXhwZWN0ZWRQYXlEYXRlOiBkZWJpdE5vdGUuZXhwZWN0ZWRQYXltZW50RGF0ZSB8fCAiIiwNCiAgICAgICAgYXBwcm92ZWRQYXlEYXRlOiAiIiwNCiAgICAgICAgYWN0dWFsUGF5RGF0ZTogZGViaXROb3RlLmFjdHVhbFBheW1lbnREYXRlIHx8ICIiLA0KDQogICAgICAgIC8vIOWPkeelqOmHkemineS/oeaBrw0KICAgICAgICBpbnZvaWNlRXhjaGFuZ2VSYXRlOiAiMSIsDQogICAgICAgIGludm9pY2VDdXJyZW5jeUNvZGU6IGRlYml0Tm90ZS5kbkN1cnJlbmN5Q29kZSB8fCAiUk1CIiwNCiAgICAgICAgaW52b2ljZU5ldEFtb3VudDogZGViaXROb3RlLmlzUmVjZWl2YWJsZSA/IGRlYml0Tm90ZS5iaWxsUmVjZWl2YWJsZSA6IGRlYml0Tm90ZS5iaWxsUGF5YWJsZSwNCiAgICAgICAgaW52b2ljZVN0YXR1czogZGViaXROb3RlLmludm9pY2VTdGF0dXMgPT09ICJpc3N1ZWQiID8gIjEiIDogIjAiLA0KICAgICAgICBiZWxvbmdzVG9Nb250aDogdGhpcy5mb3JtYXRDdXJyZW50TW9udGgoKSwNCg0KICAgICAgICAvLyBSQ1TlhbPogZTkv6Hmga8NCiAgICAgICAgcmN0SWQ6IHRoaXMucmN0SWQgfHwgbnVsbA0KICAgICAgfQ0KDQogICAgICAvLyDlh4blpIflj5HnpajmmI7nu4bpobkNCiAgICAgIHRoaXMuaW52b2ljZUl0ZW1zID0gZGViaXROb3RlLnJzQ2hhcmdlTGlzdCA/IGRlYml0Tm90ZS5yc0NoYXJnZUxpc3QubWFwKGNoYXJnZSA9PiB7DQogICAgICAgIHJldHVybiB7DQogICAgICAgICAgYmlsbE5vOiBkZWJpdE5vdGUuYmlsbE5vIHx8ICIiLA0KICAgICAgICAgIHJjdE5vOiAiIiwgIC8vIOi/memHjOWPr+iDvemcgOimgeS7jueItue7hOS7tuiOt+WPlg0KICAgICAgICAgIHNlcnZpY2VUeXBlOiBjaGFyZ2UuY2hhcmdlTmFtZSB8fCAiIiwNCiAgICAgICAgICBjaGFyZ2VOYW1lOiBjaGFyZ2UuY2hhcmdlTmFtZSB8fCAiIiwNCiAgICAgICAgICByZW1hcms6IGNoYXJnZS5yZW1hcmsgfHwgIiIsDQogICAgICAgICAgcGF5bWVudEZsYWc6IGRlYml0Tm90ZS5pc1JlY2lldmluZ09yUGF5aW5nID09IDAgPyAi5pS2IiA6ICLku5giLA0KICAgICAgICAgIHF1b3RlQ3VycmVuY3k6IGNoYXJnZS5jdXJyZW5jeUNvZGUgfHwgIiIsDQogICAgICAgICAgdW5pdFByaWNlOiBjaGFyZ2UudW5pdFByaWNlIHx8IDAsDQogICAgICAgICAgcXVhbnRpdHk6IGNoYXJnZS5xdWFudGl0eSB8fCAwLA0KICAgICAgICAgIHVuaXQ6IGNoYXJnZS51bml0IHx8ICIiLA0KICAgICAgICAgIHNldHRsZW1lbnRSYXRlOiBjaGFyZ2UuZXhjaGFuZ2VSYXRlIHx8IDEsDQogICAgICAgICAgc2V0dGxlbWVudEN1cnJlbmN5OiBkZWJpdE5vdGUuZG5DdXJyZW5jeUNvZGUgfHwgIiIsDQogICAgICAgICAgdGF4UmF0ZTogY2hhcmdlLnRheFJhdGUgfHwgIiIsDQogICAgICAgICAgdGF4SW5jbHVkZWRUb3RhbDogY2hhcmdlLnN1YnRvdGFsIHx8IDAsDQogICAgICAgICAgaW52b2ljZUl0ZW1OYW1lOiBjaGFyZ2UuY2hhcmdlTmFtZSB8fCAiIiwNCiAgICAgICAgICB0YXhDb2RlOiAiIg0KICAgICAgICB9DQogICAgICB9KSA6IFtdDQogICAgfSwNCg0KICAgIC8vIOWHhuWkh+WPkeelqOWvueivneahhuaVsOaNru+8iOS9v+eUqOeOsOacieWPkeelqOS/oeaBr++8iQ0KICAgIHByZXBhcmVJbnZvaWNlRGF0YVdpdGhFeGlzdGluZyhkZWJpdE5vdGUsIGV4aXN0aW5nSW52b2ljZSkgew0KICAgICAgLy8g5L2/55So546w5pyJ5Y+R56Wo5L+h5oGv6K6+572u6KGo5Y2V5pWw5o2uDQogICAgICB0aGlzLmludm9pY2VGb3JtID0gew0KICAgICAgICAvLyDln7rmnKzlj5Hnpajkv6Hmga8NCiAgICAgICAgaW52b2ljZUlkOiBleGlzdGluZ0ludm9pY2UuaW52b2ljZUlkIHx8IGRlYml0Tm90ZS5pbnZvaWNlSWQgfHwgbnVsbCwNCiAgICAgICAgaW52b2ljZUNvZGVObzogZXhpc3RpbmdJbnZvaWNlLmludm9pY2VDb2RlTm8gfHwgZGViaXROb3RlLmludm9pY2VDb2RlTm8gfHwgIiIsDQogICAgICAgIHNhbGVCdXk6IGV4aXN0aW5nSW52b2ljZS5zYWxlQnV5IHx8IChkZWJpdE5vdGUuaXNSZWNpZXZpbmdPclBheWluZyA9PSAwID8gInNhbGUiIDogImJ1eSIpLA0KICAgICAgICB0YXhDbGFzczogZXhpc3RpbmdJbnZvaWNlLnRheENsYXNzIHx8ICIiLA0KICAgICAgICBpbnZvaWNlVHlwZTogZXhpc3RpbmdJbnZvaWNlLmludm9pY2VUeXBlIHx8ICLlop7lgLznqI7lj5HnpagiLA0KICAgICAgICBtZXJnZUludm9pY2U6IGV4aXN0aW5nSW52b2ljZS5tZXJnZUludm9pY2UgfHwgZGViaXROb3RlLm1lcmdlSW52b2ljZSB8fCAiMCIsDQogICAgICAgIGludm9pY2VPZmZpY2FsTm86IGV4aXN0aW5nSW52b2ljZS5pbnZvaWNlT2ZmaWNhbE5vIHx8IGRlYml0Tm90ZS5pbnZvaWNlT2ZmaWNhbE5vIHx8ICIiLA0KDQogICAgICAgIC8vIOWFrOWPuOWSjOi0puaIt+S/oeaBrw0KICAgICAgICBpbnZvaWNlQmVsb25nc1RvOiBleGlzdGluZ0ludm9pY2UuaW52b2ljZUJlbG9uZ3NUbyB8fCBkZWJpdE5vdGUuY29tcGFueUJlbG9uZ3NUbyB8fCAiIiwNCiAgICAgICAgcmljaEJhbmtDb2RlOiBleGlzdGluZ0ludm9pY2UucmljaEJhbmtDb2RlIHx8IGRlYml0Tm90ZS5iYW5rQWNjb3VudENvZGUgfHwgIiIsDQogICAgICAgIGNvb3BlcmF0b3JJZDogZXhpc3RpbmdJbnZvaWNlLmNvb3BlcmF0b3JJZCB8fCBkZWJpdE5vdGUuY2xlYXJpbmdDb21wYW55SWQgfHwgIiIsDQogICAgICAgIGNvb3BlcmF0b3JCYW5rQ29kZTogZXhpc3RpbmdJbnZvaWNlLmNvb3BlcmF0b3JCYW5rQ29kZSB8fCBkZWJpdE5vdGUuY2xlYXJpbmdDb21wYW55QmFua0FjY291bnQgfHwgIiIsDQogICAgICAgIHJpY2hDb21wYW55VGl0bGU6IGV4aXN0aW5nSW52b2ljZS5yaWNoQ29tcGFueVRpdGxlIHx8IGRlYml0Tm90ZS5iYW5rQWNjb3VudE5hbWUgfHwgIiIsDQogICAgICAgIGNvb3BlcmF0b3JDb21wYW55VGl0bGU6IGV4aXN0aW5nSW52b2ljZS5jb29wZXJhdG9yQ29tcGFueVRpdGxlIHx8IGRlYml0Tm90ZS5jbGVhcmluZ0NvbXBhbnlOYW1lIHx8ICIiLA0KDQogICAgICAgIC8vIOmhueebruWSjOiuouWNleS/oeaBrw0KICAgICAgICBvZmZpY2FsQ2hhcmdlTmFtZVN1bW1hcnk6IGV4aXN0aW5nSW52b2ljZS5vZmZpY2FsQ2hhcmdlTmFtZVN1bW1hcnkgfHwgIiIsDQogICAgICAgIHJlbGF0ZWRPcmRlck5vOiBleGlzdGluZ0ludm9pY2UucmVsYXRlZE9yZGVyTm8gfHwgIiIsDQoNCiAgICAgICAgLy8g56iO5Y+35ZKM6ZO26KGM5L+h5oGvDQogICAgICAgIHJpY2hWYXRTZXJpYWxObzogZXhpc3RpbmdJbnZvaWNlLnJpY2hWYXRTZXJpYWxObyB8fCAiIiwNCiAgICAgICAgY29vcGVyYXRvclZhdFNlcmlhbE5vOiBleGlzdGluZ0ludm9pY2UuY29vcGVyYXRvclZhdFNlcmlhbE5vIHx8ICIiLA0KICAgICAgICByaWNoQmFua0Z1bGxuYW1lOiBleGlzdGluZ0ludm9pY2UucmljaEJhbmtGdWxsbmFtZSB8fCAiIiwNCiAgICAgICAgY29vcGVyYXRvckJhbmtGdWxsbmFtZTogZXhpc3RpbmdJbnZvaWNlLmNvb3BlcmF0b3JCYW5rRnVsbG5hbWUgfHwgIiIsDQogICAgICAgIHJpY2hCYW5rQWNjb3VudDogZXhpc3RpbmdJbnZvaWNlLnJpY2hCYW5rQWNjb3VudCB8fCAiIiwNCiAgICAgICAgY29vcGVyYXRvckJhbmtBY2NvdW50OiBleGlzdGluZ0ludm9pY2UuY29vcGVyYXRvckJhbmtBY2NvdW50IHx8IGRlYml0Tm90ZS5jbGVhcmluZ0NvbXBhbnlCYW5rQWNjb3VudCB8fCAiIiwNCg0KICAgICAgICAvLyDlpIfms6jlkozml6XmnJ/kv6Hmga8NCiAgICAgICAgaW52b2ljZVJlbWFyazogZXhpc3RpbmdJbnZvaWNlLmludm9pY2VSZW1hcmsgfHwgIiIsDQogICAgICAgIGV4cGVjdGVkUGF5RGF0ZTogZXhpc3RpbmdJbnZvaWNlLmV4cGVjdGVkUGF5RGF0ZSB8fCBkZWJpdE5vdGUuZXhwZWN0ZWRQYXltZW50RGF0ZSB8fCAiIiwNCiAgICAgICAgYXBwcm92ZWRQYXlEYXRlOiBleGlzdGluZ0ludm9pY2UuYXBwcm92ZWRQYXlEYXRlIHx8ICIiLA0KICAgICAgICBhY3R1YWxQYXlEYXRlOiBleGlzdGluZ0ludm9pY2UuYWN0dWFsUGF5RGF0ZSB8fCBkZWJpdE5vdGUuYWN0dWFsUGF5bWVudERhdGUgfHwgIiIsDQoNCiAgICAgICAgLy8g5Y+R56Wo6YeR6aKd5L+h5oGvDQogICAgICAgIGludm9pY2VFeGNoYW5nZVJhdGU6IGV4aXN0aW5nSW52b2ljZS5pbnZvaWNlRXhjaGFuZ2VSYXRlIHx8ICIxIiwNCiAgICAgICAgaW52b2ljZUN1cnJlbmN5Q29kZTogZXhpc3RpbmdJbnZvaWNlLmludm9pY2VDdXJyZW5jeUNvZGUgfHwgZGViaXROb3RlLmRuQ3VycmVuY3lDb2RlIHx8ICJSTUIiLA0KICAgICAgICBpbnZvaWNlTmV0QW1vdW50OiBleGlzdGluZ0ludm9pY2UuaW52b2ljZU5ldEFtb3VudCB8fCAoZGViaXROb3RlLmlzUmVjZWl2YWJsZSA/IGRlYml0Tm90ZS5iaWxsUmVjZWl2YWJsZSA6IGRlYml0Tm90ZS5iaWxsUGF5YWJsZSksDQogICAgICAgIGludm9pY2VTdGF0dXM6IGV4aXN0aW5nSW52b2ljZS5pbnZvaWNlU3RhdHVzIHx8IChkZWJpdE5vdGUuaW52b2ljZVN0YXR1cyA9PT0gImlzc3VlZCIgPyAiMSIgOiAiMCIpLA0KICAgICAgICBiZWxvbmdzVG9Nb250aDogZXhpc3RpbmdJbnZvaWNlLmJlbG9uZ3NUb01vbnRoIHx8IHRoaXMuZm9ybWF0Q3VycmVudE1vbnRoKCksDQoNCiAgICAgICAgLy8gUkNU5YWz6IGU5L+h5oGvDQogICAgICAgIHJjdElkOiBleGlzdGluZ0ludm9pY2UucmN0SWQgfHwgdGhpcy5yY3RJZCB8fCBudWxsDQogICAgICB9DQoNCiAgICAgIC8vIOS9v+eUqOeOsOacieWPkeelqOaYjue7humhue+8jOWmguaenOayoeacieWImeS9v+eUqGRlYml0Tm90ZeeahOi0ueeUqOaYjue7hg0KICAgICAgaWYgKGV4aXN0aW5nSW52b2ljZS5pbnZvaWNlSXRlbXMgJiYgZXhpc3RpbmdJbnZvaWNlLmludm9pY2VJdGVtcy5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuaW52b2ljZUl0ZW1zID0gZXhpc3RpbmdJbnZvaWNlLmludm9pY2VJdGVtcw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5YeG5aSH5Y+R56Wo5piO57uG6aG5DQogICAgICAgIHRoaXMuaW52b2ljZUl0ZW1zID0gZGViaXROb3RlLnJzQ2hhcmdlTGlzdCA/IGRlYml0Tm90ZS5yc0NoYXJnZUxpc3QubWFwKGNoYXJnZSA9PiB7DQogICAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIGJpbGxObzogZGViaXROb3RlLmJpbGxObyB8fCAiIiwNCiAgICAgICAgICAgIHJjdE5vOiAiIiwgIC8vIOi/memHjOWPr+iDvemcgOimgeS7jueItue7hOS7tuiOt+WPlg0KICAgICAgICAgICAgc2VydmljZVR5cGU6IGNoYXJnZS5jaGFyZ2VOYW1lIHx8ICIiLA0KICAgICAgICAgICAgY2hhcmdlTmFtZTogY2hhcmdlLmNoYXJnZU5hbWUgfHwgIiIsDQogICAgICAgICAgICByZW1hcms6IGNoYXJnZS5yZW1hcmsgfHwgIiIsDQogICAgICAgICAgICBwYXltZW50RmxhZzogZGViaXROb3RlLmlzUmVjaWV2aW5nT3JQYXlpbmcgPT0gMCA/ICLmlLYiIDogIuS7mCIsDQogICAgICAgICAgICBxdW90ZUN1cnJlbmN5OiBjaGFyZ2UuY3VycmVuY3lDb2RlIHx8ICIiLA0KICAgICAgICAgICAgdW5pdFByaWNlOiBjaGFyZ2UudW5pdFByaWNlIHx8IDAsDQogICAgICAgICAgICBxdWFudGl0eTogY2hhcmdlLnF1YW50aXR5IHx8IDAsDQogICAgICAgICAgICB1bml0OiBjaGFyZ2UudW5pdCB8fCAiIiwNCiAgICAgICAgICAgIHNldHRsZW1lbnRSYXRlOiBjaGFyZ2UuZXhjaGFuZ2VSYXRlIHx8IDEsDQogICAgICAgICAgICBzZXR0bGVtZW50Q3VycmVuY3k6IGRlYml0Tm90ZS5kbkN1cnJlbmN5Q29kZSB8fCAiIiwNCiAgICAgICAgICAgIHRheFJhdGU6IGNoYXJnZS50YXhSYXRlIHx8ICIiLA0KICAgICAgICAgICAgdGF4SW5jbHVkZWRUb3RhbDogY2hhcmdlLnN1YnRvdGFsIHx8IDAsDQogICAgICAgICAgICBpbnZvaWNlSXRlbU5hbWU6IGNoYXJnZS5jaGFyZ2VOYW1lIHx8ICIiLA0KICAgICAgICAgICAgdGF4Q29kZTogIiINCiAgICAgICAgICB9DQogICAgICAgIH0pIDogW10NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5qC85byP5YyW5b2T5YmN5pyI5Lu95Li6IHl5eXlNTSDmoLzlvI/vvIjlpoIgMjAyNTAz77yJDQogICAgZm9ybWF0Q3VycmVudE1vbnRoKCkgew0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCkNCiAgICAgIGNvbnN0IHllYXIgPSBkYXRlLmdldEZ1bGxZZWFyKCkNCiAgICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKGRhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICIwIikNCiAgICAgIHJldHVybiBgJHt5ZWFyfSR7bW9udGh9YA0KICAgIH0sDQoNCiAgICAvLyDlpITnkIblj5Hnpajlr7nor53moYbmj5DkuqQNCiAgICBhc3luYyBoYW5kbGVJbnZvaWNlU3VibWl0KGZvcm1EYXRhKSB7DQogICAgICB0cnkgew0KICAgICAgICBsZXQgcmVzcG9uc2UNCg0KICAgICAgICAvLyDmoLnmja7mmK/lkKbmnIlpbnZvaWNlSWTlhrPlrprmmK/mlrDlop7ov5jmmK/kv67mlLkNCiAgICAgICAgaWYgKGZvcm1EYXRhLmludm9pY2VJZCkgew0KICAgICAgICAgIC8vIOS/ruaUueWPkeelqA0KICAgICAgICAgIHJlc3BvbnNlID0gYXdhaXQgdXBkYXRlVmF0aW52b2ljZShmb3JtRGF0YSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDmlrDlop7lj5HnpagNCiAgICAgICAgICByZXNwb25zZSA9IGF3YWl0IGFkZFZhdGludm9pY2UoZm9ybURhdGEpDQogICAgICAgIH0NCg0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgLy8g5pu05paw5Y+R56Wo54q25oCBDQogICAgICAgICAgaWYgKHRoaXMuY3VycmVudERlYml0Tm90ZSkgew0KICAgICAgICAgICAgdGhpcy5jdXJyZW50RGViaXROb3RlLmludm9pY2VTdGF0dXMgPSBmb3JtRGF0YS5pbnZvaWNlU3RhdHVzID09PSAiMSIgPyAiaXNzdWVkIiA6ICJ1bmlzc3VlZCINCg0KICAgICAgICAgICAgLy8g5bCG5Y+R56WoSUTlhpnlhaXliLBkZWJpdE5vdGXkuK0NCiAgICAgICAgICAgIGxldCBpbnZvaWNlSWQgPSBudWxsDQogICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAmJiByZXNwb25zZS5kYXRhLmludm9pY2VJZCkgew0KICAgICAgICAgICAgICBpbnZvaWNlSWQgPSByZXNwb25zZS5kYXRhLmludm9pY2VJZA0KICAgICAgICAgICAgICB0aGlzLmN1cnJlbnREZWJpdE5vdGUuaW52b2ljZUlkID0gaW52b2ljZUlkDQogICAgICAgICAgICB9IGVsc2UgaWYgKGZvcm1EYXRhLmludm9pY2VJZCkgew0KICAgICAgICAgICAgICBpbnZvaWNlSWQgPSBmb3JtRGF0YS5pbnZvaWNlSWQNCiAgICAgICAgICAgICAgdGhpcy5jdXJyZW50RGViaXROb3RlLmludm9pY2VJZCA9IGludm9pY2VJZA0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDmm7TmlrDlj5Hnpajnm7jlhbPlrZfmrrUNCiAgICAgICAgICAgIHRoaXMuY3VycmVudERlYml0Tm90ZS5pbnZvaWNlQ29kZU5vID0gZm9ybURhdGEuaW52b2ljZUNvZGVObyB8fCAiIg0KICAgICAgICAgICAgdGhpcy5jdXJyZW50RGViaXROb3RlLmludm9pY2VPZmZpY2FsTm8gPSBmb3JtRGF0YS5pbnZvaWNlT2ZmaWNhbE5vIHx8ICIiDQogICAgICAgICAgICB0aGlzLmN1cnJlbnREZWJpdE5vdGUuaW52b2ljZVR5cGUgPSBmb3JtRGF0YS5pbnZvaWNlVHlwZSB8fCAiIg0KICAgICAgICAgICAgdGhpcy5jdXJyZW50RGViaXROb3RlLm1lcmdlSW52b2ljZSA9IGZvcm1EYXRhLm1lcmdlSW52b2ljZSB8fCAiMCINCiAgICAgICAgICAgIC8vIOabtOaWsOWIhui0puWNleeahOWPkeelqOeKtuaAgeS4uuW3sueUs+ivtw0KICAgICAgICAgICAgdGhpcy5jdXJyZW50RGViaXROb3RlLmludm9pY2VTdGF0dXMgPSAiYXBwbGllZCINCg0KICAgICAgICAgICAgLy8g6LCD55So5o6l5Y+j5pu05pawZGViaXROb3Rl5Lit55qEaW52b2ljZUlkDQogICAgICAgICAgICBpZiAoaW52b2ljZUlkICYmIHRoaXMuY3VycmVudERlYml0Tm90ZS5kZWJpdE5vdGVJZCkgew0KICAgICAgICAgICAgICB0cnkgew0KICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZURhdGEgPSB7DQogICAgICAgICAgICAgICAgICBkZWJpdE5vdGVJZDogdGhpcy5jdXJyZW50RGViaXROb3RlLmRlYml0Tm90ZUlkLA0KICAgICAgICAgICAgICAgICAgaW52b2ljZUlkOiBpbnZvaWNlSWQsDQogICAgICAgICAgICAgICAgICBpbnZvaWNlQ29kZU5vOiBmb3JtRGF0YS5pbnZvaWNlQ29kZU5vIHx8ICIiLA0KICAgICAgICAgICAgICAgICAgaW52b2ljZU9mZmljYWxObzogZm9ybURhdGEuaW52b2ljZU9mZmljYWxObyB8fCAiIiwNCiAgICAgICAgICAgICAgICAgIGludm9pY2VUeXBlOiBmb3JtRGF0YS5pbnZvaWNlVHlwZSB8fCAiIiwNCiAgICAgICAgICAgICAgICAgIG1lcmdlSW52b2ljZTogZm9ybURhdGEubWVyZ2VJbnZvaWNlIHx8ICIwIiwNCiAgICAgICAgICAgICAgICAgIGludm9pY2VTdGF0dXM6ICJhcHBsaWVkIg0KICAgICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICAgIC8vIOabtOaWsOWIhui0puWNlQ0KICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZVJlc3BvbnNlID0gYXdhaXQgdXBkYXRlRGViaXROb3RlKHVwZGF0ZURhdGEpDQogICAgICAgICAgICAgICAgaWYgKHVwZGF0ZVJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlj5Hnpajkv6Hmga/lt7Lmm7TmlrDliLDliIbotKbljZUiKQ0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oIuabtOaWsOWIhui0puWNleWPkeelqOS/oeaBr+Wksei0pToiLCB1cGRhdGVSZXNwb25zZS5tc2cpDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9IGNhdGNoICh1cGRhdGVFcnJvcikgew0KICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuabtOaWsOWIhui0puWNleWPkeelqOS/oeaBr+Wksei0pToiLCB1cGRhdGVFcnJvcikNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDpgJrnn6XniLbnu4Tku7bmlbDmja7lj5jljJYNCiAgICAgICAgICAgIHRoaXMuJGVtaXQoInJldHVybiIsIHRoaXMuZGViaXROb3RlTGlzdCkNCiAgICAgICAgICB9DQoNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWPkeelqOS/neWtmOaIkOWKnyIpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXNwb25zZS5tc2cgfHwgIuWPkeelqOS/neWtmOWksei0pSIpDQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuWPkeelqOS/neWtmOWksei0pToiLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Y+R56Wo5L+d5a2Y5aSx6LSl77yM6K+36YeN6K+VIikNCiAgICAgIH0NCg0KICAgICAgdGhpcy5pbnZvaWNlRGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgfSwNCg0KICAgIC8vIOWkhOeQhuWPkeelqOWvueivneahhuWPlua2iA0KICAgIGhhbmRsZUludm9pY2VDYW5jZWwoKSB7DQogICAgICB0aGlzLmludm9pY2VEaWFsb2dWaXNpYmxlID0gZmFsc2UNCiAgICB9LA0KICAgIGFwcGx5VW5sb2NrKHJvdykgew0KICAgICAgLy8g5p+l55yL5Y+R56Wo54q25oCBLOW3suW8gOelqOeahOS4jeiDveeUs+ivt+ino+mUgQ0KICAgICAgaWYgKHJvdy5pbnZvaWNlU3RhdHVzID09PSAiaXNzdWVkIikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLlt7LlvIDnpajnmoTliIbotKbljZXkuI3og73nlLPor7fop6PplIEiKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy4kZW1pdCgiYXBwbHlVbmxvY2siLCByb3cpDQogICAgfSwNCiAgICBzZXRDb21wbGV0ZShyb3cpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMuJGNvbmZpcm0oIuehruWumuimgeWwhuivpeWIhui0puWNleiuvue9ruS4uuW3suehruiupOeKtuaAgeWQl++8n+atpOaTjeS9nOWwhuemgeeUqOaVtOadoeaVsOaNruWPiuWFtui0ueeUqOaYjue7huOAgiIsICLmj5DnpLoiLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICAvLyDkv67mlLnnirbmgIHkuLrlt7Lnoa7orqQNCiAgICAgICAgICByb3cuYmlsbFN0YXR1cyA9ICJjb25maXJtZWQiDQoNCiAgICAgICAgICAvLyDorr7nva7miYDmnInotLnnlKjmmI7nu4bnmoRpc0FjY291bnRDb25maXJtZWTkuLonMSfvvIzkvb/lhbbooqvnpoHnlKgNCiAgICAgICAgICBpZiAocm93LnJzQ2hhcmdlTGlzdCAmJiByb3cucnNDaGFyZ2VMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHJvdy5yc0NoYXJnZUxpc3QuZm9yRWFjaChjaGFyZ2UgPT4gew0KICAgICAgICAgICAgICBjaGFyZ2UuaXNBY2NvdW50Q29uZmlybWVkID0gIjEiDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOmAmuefpeeItue7hOS7tueKtuaAgeWPmOabtA0KICAgICAgICAgIHRoaXMuJGVtaXQoInNldENvbXBsZXRlIiwgcm93KQ0KDQogICAgICAgICAgLy8g5o+Q56S655So5oi3DQogICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgICBtZXNzYWdlOiAi5YiG6LSm5Y2V5bey6K6+572u5Li65bey56Gu6K6k54q25oCBIg0KICAgICAgICAgIH0pDQogICAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICAvLyDnlKjmiLflj5bmtojmk43kvZwNCiAgICAgICAgfSkNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoIuiuvue9ruWIhui0puWNleeKtuaAgeWksei0pToiLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K6+572u5YiG6LSm5Y2V54q25oCB5aSx6LSlIikNCiAgICAgIH0NCiAgICB9LA0KICAgIGNoYW5nZUN1cnJlbmN5KHJvdywgY3VycmVuY3kpIHsNCiAgICAgIHJvdy5kbkN1cnJlbmN5Q29kZSA9IGN1cnJlbmN5DQogICAgfSwNCiAgICBzZWxlY3RCYW5rQWNjb3VudChyb3csIGJhbmtBY2NvdW50KSB7DQogICAgICByb3cuYmFua0FjY291bnRDb2RlID0gYmFua0FjY291bnQuYmFua0FjY291bnRDb2RlDQogICAgICByb3cuYmFua0FjY291bnROYW1lID0gYmFua0FjY291bnQuYmFua0FjY291bnROYW1lDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3RDb21wYW55KHJvdywgY29tcGFueSkgew0KICAgICAgcm93LmNsZWFyaW5nQ29tcGFueUlkID0gY29tcGFueS5jb21wYW55SWQNCiAgICAgIHJvdy5jbGVhcmluZ0NvbXBhbnlOYW1lID0gY29tcGFueS5jb21wYW55U2hvcnROYW1lDQogICAgfSwNCiAgICBhZGREZWJpdE5vdGUoKSB7DQogICAgICB0aGlzLiRlbWl0KCJhZGREZWJpdE5vdGUiKQ0KICAgIH0sDQogICAgY3VycmVuY3ksDQoNCiAgICAvLyDlsZXlvIAv5pS26LW36KGMDQogICAgaGFuZGxlRXhwYW5kQ2hhbmdlKHJvdywgZXhwYW5kZWRSb3dzKSB7DQogICAgICB0aGlzLmV4cGFuZGVkUm93cyA9IGV4cGFuZGVkUm93cw0KICAgIH0sDQoNCiAgICAvLyDliJvlu7rliIbotKbljZUNCiAgICBhc3luYyBjcmVhdGVEZWJpdE5vdGUocm93KSB7DQogICAgICB0cnkgew0KDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCLliJvlu7rliIbotKbljZXlpLHotKU6IiwgZXJyb3IpDQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuWIm+W7uuWIhui0puWNleWksei0pSIpDQogICAgICB9DQogICAgfSwNCiAgICAvLyDliKDpmaTliIbotKbljZUNCiAgICBhc3luYyBkZWxldGVEZWJpdE5vdGUocm93KSB7DQogICAgICB0cnkgew0KICAgICAgICBhd2FpdCB0aGlzLiRjb25maXJtKCLnoa7lrpropoHliKDpmaTor6XliIbotKbljZXlkJfvvJ8iLCAi5o+Q56S6Iiwgew0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIg0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLiRlbWl0KCJkZWxldGVJdGVtIiwgcm93KQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgaWYgKGVycm9yICE9PSAiY2FuY2VsIikgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuWIoOmZpOWIhui0puWNleWksei0pToiLCBlcnJvcikNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliKDpmaTliIbotKbljZXlpLHotKUiKQ0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkhOeQhui0ueeUqOaVsOaNruWPmOWMlg0KICAgIGhhbmRsZUNoYXJnZURhdGFDaGFuZ2Uocm93LCBjaGFyZ2VEYXRhKSB7DQogICAgICBsZXQgYmlsbFJlY2VpdmFibGUgPSAwDQogICAgICBsZXQgYmlsbFBheWFibGUgPSAwDQoNCiAgICAgIC8vIOe7n+iuoWNoYXJnZURhdGHnmoTotLnnlKgNCiAgICAgIGlmICh0aGlzLmlzUmVjZWl2YWJsZSkgew0KICAgICAgICAvLyDlupTmlLYNCiAgICAgICAgY2hhcmdlRGF0YS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIC8vIOS9v+eUqGN1cnJlbmN5Lmpz6K6h566XDQogICAgICAgICAgYmlsbFJlY2VpdmFibGUgPSBjdXJyZW5jeShiaWxsUmVjZWl2YWJsZSkuYWRkKGl0ZW0uc3VidG90YWwpLnRvU3RyaW5nKCkNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOW6lOS7mA0KICAgICAgICBjaGFyZ2VEYXRhLmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgYmlsbFBheWFibGUgPSBjdXJyZW5jeShiaWxsUGF5YWJsZSkuYWRkKGl0ZW0uc3VidG90YWwpLnRvU3RyaW5nKCkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIHJvdy5iaWxsUmVjZWl2YWJsZSA9IGJpbGxSZWNlaXZhYmxlDQogICAgICByb3cuYmlsbFBheWFibGUgPSBiaWxsUGF5YWJsZQ0KDQogICAgICByb3cucnNDaGFyZ2VMaXN0ID0gY2hhcmdlRGF0YQ0KICAgICAgLy8g6YCa55+l54i257uE5Lu25pWw5o2u5Y+Y5YyWDQogICAgICB0aGlzLiRlbWl0KCJyZXR1cm4iLCB0aGlzLmRlYml0Tm90ZUxpc3QpDQogICAgfSwNCg0KICAgIC8vIOWkhOeQhui0ueeUqOmAieaLqQ0KICAgIGhhbmRsZUNoYXJnZVNlbGVjdGlvbihyb3csIHNlbGVjdGVkQ2hhcmdlcykgew0KICAgICAgY29uc3QgaW5kZXggPSB0aGlzLmxvY2FsRGViaXROb3RlTGlzdC5maW5kSW5kZXgoaXRlbSA9PiBpdGVtID09PSByb3cpDQogICAgICBpZiAoaW5kZXggIT09IC0xKSB7DQogICAgICAgIHRoaXMubG9jYWxEZWJpdE5vdGVMaXN0W2luZGV4XS5zZWxlY3RlZENoYXJnZXMgPSBzZWxlY3RlZENoYXJnZXMNCiAgICAgICAgLy8g6YCa55+l54i257uE5Lu25pWw5o2u5Y+Y5YyWDQogICAgICAgIHRoaXMuJGVtaXQoInVwZGF0ZTpkZWJpdE5vdGVMaXN0IiwgdGhpcy5sb2NhbERlYml0Tm90ZUxpc3QpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWkjeWItui0ueeUqA0KICAgIGhhbmRsZUNvcHlGcmVpZ2h0KGNoYXJnZSkgew0KICAgICAgdGhpcy4kZW1pdCgiY29weUZyZWlnaHQiLCBjaGFyZ2UpDQogICAgfSwNCg0KICAgIC8vIOWIoOmZpOi0ueeUqOmhuQ0KICAgIGhhbmRsZURlbGV0ZUl0ZW0oY2hhcmdlKSB7DQogICAgICB0aGlzLiRlbWl0KCJkZWxldGVJdGVtIiwgY2hhcmdlKQ0KICAgIH0sDQoNCiAgICAvLyDliKDpmaTmiYDmnInotLnnlKgNCiAgICBoYW5kbGVEZWxldGVBbGwoKSB7DQogICAgICB0aGlzLiRlbWl0KCJkZWxldGVBbGwiKQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5botKbljZXnirbmgIHnsbvlnosNCiAgICBnZXRCaWxsU3RhdHVzVHlwZShzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgImRyYWZ0IjogImluZm8iLA0KICAgICAgICAiY29uZmlybWVkIjogInN1Y2Nlc3MiLA0KICAgICAgICAiY2xvc2VkIjogImRhbmdlciINCiAgICAgIH0NCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAiaW5mbyINCiAgICB9LA0KDQogICAgLy8g6I635Y+W6LSm5Y2V54q25oCB5paH5pysDQogICAgZ2V0QmlsbFN0YXR1c1RleHQoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICJkcmFmdCI6ICLojYnnqL8iLA0KICAgICAgICAiY29uZmlybWVkIjogIuW3suehruiupCIsDQogICAgICAgICJjbG9zZWQiOiAi5bey5YWz6ZetIg0KICAgICAgfQ0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICLmnKrnn6UiDQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWPkeelqOeKtuaAgeexu+Weiw0KICAgIGdldEludm9pY2VTdGF0dXNUeXBlKHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAidW5pc3N1ZWQiOiAiaW5mbyIsDQogICAgICAgICJpc3N1ZWQiOiAic3VjY2VzcyIsDQogICAgICAgICJhcHBsaWVkIjogIndhcm5pbmciLA0KICAgICAgICAiY2FuY2VsZWQiOiAiZGFuZ2VyIg0KICAgICAgfQ0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICJpbmZvIg0KICAgIH0sDQoNCiAgICAvLyDojrflj5blj5HnpajnirbmgIHmlofmnKwNCiAgICBnZXRJbnZvaWNlU3RhdHVzVGV4dChzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgInVuaXNzdWVkIjogIuacquW8gOelqCIsDQogICAgICAgICJpc3N1ZWQiOiAi5bey5byA56WoIiwNCiAgICAgICAgImFwcGxpZWQiOiAi5bey55Sz6K+3IiwNCiAgICAgICAgImNhbmNlbGVkIjogIuW3suS9nOW6nyINCiAgICAgIH0NCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAi5pyq55+lIg0KICAgIH0sDQoNCiAgICAvLyDojrflj5bplIDotKbnirbmgIHnsbvlnosNCiAgICBnZXRXcml0ZW9mZlN0YXR1c1R5cGUoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICJ1bndyaXR0ZW4iOiAiaW5mbyIsDQogICAgICAgICJwYXJ0aWFsIjogIndhcm5pbmciLA0KICAgICAgICAid3JpdHRlbiI6ICJzdWNjZXNzIg0KICAgICAgfQ0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICJpbmZvIg0KICAgIH0sDQoNCiAgICAvLyDojrflj5bplIDotKbnirbmgIHmlofmnKwNCiAgICBnZXRXcml0ZW9mZlN0YXR1c1RleHQoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICJ1bndyaXR0ZW4iOiAi5pyq6ZSA6LSmIiwNCiAgICAgICAgInBhcnRpYWwiOiAi6YOo5YiG6ZSA6LSmIiwNCiAgICAgICAgIndyaXR0ZW4iOiAi5bey6ZSA6LSmIg0KICAgICAgfQ0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICLmnKrnn6UiDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["debitNodeList.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4NA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "debitNodeList.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <div class=\"debit-note-list\">\r\n    <el-table\r\n      ref=\"debitNoteTable\"\r\n      :data=\"debitNoteList\"\r\n      border\r\n      style=\"width: 100%\"\r\n      @expand-change=\"handleExpandChange\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <!-- 可展开列 -->\r\n      <el-table-column type=\"expand\" width=\"50\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- 嵌套 chargeList 组件 -->\r\n          <debit-note-charge-list\r\n            :charge-data=\"scope.row.rsChargeList\"\r\n            :company-list=\"companyList\"\r\n            :debit-note=\"scope.row\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            :hidden-supplier=\"hiddenSupplier\"\r\n            :is-receivable=\"isReceivable\"\r\n            :open-charge-list=\"true\"\r\n            @copyFreight=\"handleCopyFreight\"\r\n            @deleteAll=\"handleDeleteAll\"\r\n            @deleteItem=\"scope.row.rsChargeList = scope.row.rsChargeList.filter(charge => charge !== $event)\"\r\n            @return=\"handleChargeDataChange(scope.row, $event)\"\r\n            @selectRow=\"handleChargeSelection(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <!-- 勾选列 -->\r\n      <el-table-column align=\"center\" type=\"selection\"></el-table-column>\r\n\r\n      <!-- 分账单基本信息列 -->\r\n      <el-table-column label=\"所属公司\" prop=\"sqdRctNo\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"scope.row.companyBelongsTo\" :placeholder=\"'收付路径'\"\r\n                       :type=\"'rsPaymentTitle'\" @return=\"scope.row.companyBelongsTo=$event\"\r\n                       :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"我司账户\" prop=\"companyName\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\"\r\n                       :pass=\"scope.row.bankAccountCode\" :placeholder=\"'我司账户'\"\r\n                       :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n                       :type=\"'companyAccount'\"\r\n                       @return=\"scope.row.bankAccountCode=$event\" @returnData=\"selectBankAccount(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"收付标志\" prop=\"isRecievingOrPaying\" width=\"50\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getBillStatusType(scope.row.billStatus)\"\r\n            size=\"mini\"\r\n          >\r\n            {{ scope.row.isRecievingOrPaying == 0 ? \"收\" : \"付\" }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"结算单位\" prop=\"dnCurrencyCode\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :custom-options=\"companyList\" :flat=\"false\" :multiple=\"false\"\r\n                       :pass=\"scope.row.clearingCompanyId\" :placeholder=\"'客户'\"\r\n                       :type=\"'clientCustom'\" @return=\"scope.row.clearingCompanyId=$event\"\r\n                       :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n                       @returnData=\"handleSelectCompany(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"对方账户\" prop=\"billReceivable\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input v-model=\"scope.row.clearingCompanyBankAccount\"\r\n                    :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n                    :disabled=\"debitNoteDisabled(scope.row)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"结算币种\" prop=\"billPayable\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n                       :pass=\"scope.row.dnCurrencyCode\"\r\n                       :type=\"'currency'\"\r\n                       @return=\"changeCurrency(scope.row,$event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"right\" label=\"账单应收\" v-if=\"isReceivable\" prop=\"billReceivable\" width=\"80\"/>\r\n      <el-table-column align=\"right\" label=\"账单应付\" v-if=\"!isReceivable\" prop=\"billPayable\" width=\"80\"/>\r\n\r\n      <el-table-column align=\"center\" label=\"账单状态\" prop=\"billStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getBillStatusType(scope.row.billStatus)\"\r\n            size=\"mini\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n          >\r\n            {{ getBillStatusText(scope.row.billStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"发票状态\" prop=\"invoiceStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getInvoiceStatusType(scope.row.invoiceStatus)\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n            size=\"mini\" @click=\"handleInvoiceStatusClick(scope.row)\"\r\n          >\r\n            {{ getInvoiceStatusText(scope.row.invoiceStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"申请支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n            v-model=\"scope.row.requestPaymentDate\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            placeholder=\"选择日期\" type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预计支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n            v-model=\"scope.row.expectedPaymentDate\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            placeholder=\"选择日期\" type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n            v-model=\"scope.row.actualPaymentDate\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            placeholder=\"选择日期\" type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"销账状态\" prop=\"writeoffStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getWriteoffStatusType(scope.row.writeoffStatus)\"\r\n            size=\"mini\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n          >\r\n            {{ getWriteoffStatusText(scope.row.writeoffStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column class-name=\"small-padding fixed-width\" fixed=\"right\" label=\"操作\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <div style=\"display: flex; gap: 4px;\">\r\n            <el-button\r\n              v-if=\"scope.row.billStatus==='confirmed'\"\r\n              icon=\"el-icon-unlock\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"applyUnlock(scope.row)\"\r\n            >\r\n              申请解锁\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"scope.row.billStatus==='draft'\"\r\n              icon=\"el-icon-check\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"setComplete(scope.row)\"\r\n            >\r\n              设置完成\r\n            </el-button>\r\n            <el-button\r\n              :disabled=\"scope.row.rsChargeList.length>0\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"deleteDebitNote(scope.row)\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-button :disabled=\"disabled\" style=\"padding: 0\"\r\n               type=\"text\"\r\n               @click=\"addDebitNote\"\r\n    >[＋]\r\n    </el-button>\r\n\r\n    <!-- 发票对话框 -->\r\n    <vatinvoice-dialog\r\n      :company-list=\"companyList\"\r\n      :form=\"invoiceForm\"\r\n      :invoice-items=\"invoiceItems\"\r\n      :title=\"'增值税发票管理'\"\r\n      :visible.sync=\"invoiceDialogVisible\"\r\n      @cancel=\"handleInvoiceCancel\"\r\n      @submit=\"handleInvoiceSubmit\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport debitNoteChargeList from \"@/views/system/document/debitNoteChargeList.vue\"\r\nimport VatinvoiceDialog from \"@/views/system/vatinvoice/components/VatinvoiceDialog.vue\"\r\nimport {\r\n  addVatinvoice,\r\n  updateVatinvoice,\r\n  getVatinvoice,\r\n  countVatinvoiceByRctId,\r\n  generateInvoiceCode\r\n} from \"@/api/system/vatInvoice\"\r\nimport {updateDebitNote} from \"@/api/system/debitnote\"\r\n\r\nexport default {\r\n  name: \"debitNoteList\",\r\n  components: {debitNoteChargeList, VatinvoiceDialog},\r\n  props: [\r\n    \"companyList\",\r\n    \"disabled\",\r\n    \"hiddenSupplier\",\r\n    \"rctId\",\r\n    \"debitNoteList\",\r\n    \"isReceivable\"\r\n  ],\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      expandedRows: [],\r\n      localDebitNoteList: [],\r\n      selectedDebitNotes: [],\r\n      invoiceDialogVisible: false,\r\n      currentDebitNote: null,\r\n      invoiceForm: {},\r\n      invoiceItems: []\r\n    }\r\n  },\r\n  watch: {\r\n    debitNoteList: {\r\n      immediate: true,\r\n      deep: true,\r\n      handler(newVal) {\r\n        this.$emit(\"update:debitNoteList\", newVal)\r\n        this.$emit(\"return\", newVal)\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  methods: {\r\n    // 处理表格选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedDebitNotes = selection\r\n      this.$emit(\"selection-change\", selection)\r\n    },\r\n    debitNoteDisabled(row) {\r\n      return row.billStatus === \"confirmed\" || this.disabled\r\n    },\r\n    async handleInvoiceStatusClick(row) {\r\n      // 申请开票\r\n      this.currentDebitNote = row\r\n\r\n      // 检查是否有invoiceId\r\n      if (row.invoiceId) {\r\n        try {\r\n          // 根据invoiceId查找现有发票信息\r\n          const response = await getVatinvoice(row.invoiceId)\r\n          if (response.code === 200 && response.data) {\r\n            // 使用现有发票信息准备数据\r\n            this.prepareInvoiceDataWithExisting(row, response.data)\r\n          } else {\r\n            // 查找失败，使用默认数据\r\n            await this.prepareInvoiceData(row)\r\n          }\r\n        } catch (error) {\r\n          console.error(\"获取发票信息失败:\", error)\r\n          // 出错时使用默认数据\r\n          await this.prepareInvoiceData(row)\r\n        }\r\n      } else {\r\n        // 没有invoiceId，创建新的发票\r\n        await this.prepareInvoiceData(row)\r\n      }\r\n\r\n      this.invoiceDialogVisible = true\r\n    },\r\n\r\n    // 生成发票流水号\r\n    async generateInvoiceCodeNo(rctId, cooperatorId) {\r\n      try {\r\n        // 调用API生成发票编码\r\n        const response = await generateInvoiceCode(rctId, cooperatorId)\r\n        if (response.code === 200) {\r\n          return response.msg\r\n        }\r\n      } catch (error) {\r\n        console.error(\"生成发票编码失败:\", error)\r\n      }\r\n      // 如果API调用失败，返回默认格式\r\n      return \"\"\r\n    },\r\n\r\n    // 准备发票对话框数据（新建发票）\r\n    async prepareInvoiceData(debitNote) {\r\n      // 生成发票流水号\r\n      let invoiceCodeNo\r\n      if (this.rctId && debitNote.clearingCompanyId) {\r\n        let invoiceCode = await this.generateInvoiceCodeNo(this.rctId, debitNote.clearingCompanyId)\r\n        invoiceCodeNo = debitNote.sqdRctNo + \"-\" + invoiceCode\r\n      }\r\n\r\n      // 设置发票表单数据\r\n      this.invoiceForm = {\r\n        // 基本发票信息\r\n        invoiceId: debitNote.invoiceId || null, // 发票ID\r\n        invoiceCodeNo: invoiceCodeNo,\r\n        saleBuy: debitNote.isRecievingOrPaying == 0 ? \"sale\" : \"buy\", // 根据收付标志设置进销项\r\n        taxClass: \"\",\r\n        invoiceType: \"增值税发票\",\r\n        mergeInvoice: debitNote.mergeInvoice || \"0\",\r\n        invoiceOfficalNo: debitNote.invoiceOfficalNo || \"\",\r\n\r\n        // 公司和账户信息\r\n        invoiceBelongsTo: debitNote.companyBelongsTo || \"\",\r\n        richBankCode: debitNote.bankAccountCode || \"\",\r\n        cooperatorId: debitNote.clearingCompanyId || \"\",\r\n        cooperatorBankCode: debitNote.clearingCompanyBankAccount || \"\",\r\n        richCompanyTitle: debitNote.bankAccountName || \"\",\r\n        cooperatorCompanyTitle: debitNote.clearingCompanyName || \"\",\r\n\r\n        // 项目和订单信息\r\n        officalChargeNameSummary: \"\",\r\n        relatedOrderNo: \"\",\r\n\r\n        // 税号和银行信息\r\n        richVatSerialNo: \"\",\r\n        cooperatorVatSerialNo: \"\",\r\n        richBankFullname: \"\",\r\n        cooperatorBankFullname: \"\",\r\n        richBankAccount: \"\",\r\n        cooperatorBankAccount: debitNote.clearingCompanyBankAccount || \"\",\r\n\r\n        // 备注和日期信息\r\n        invoiceRemark: \"\",\r\n        expectedPayDate: debitNote.expectedPaymentDate || \"\",\r\n        approvedPayDate: \"\",\r\n        actualPayDate: debitNote.actualPaymentDate || \"\",\r\n\r\n        // 发票金额信息\r\n        invoiceExchangeRate: \"1\",\r\n        invoiceCurrencyCode: debitNote.dnCurrencyCode || \"RMB\",\r\n        invoiceNetAmount: debitNote.isReceivable ? debitNote.billReceivable : debitNote.billPayable,\r\n        invoiceStatus: debitNote.invoiceStatus === \"issued\" ? \"1\" : \"0\",\r\n        belongsToMonth: this.formatCurrentMonth(),\r\n\r\n        // RCT关联信息\r\n        rctId: this.rctId || null\r\n      }\r\n\r\n      // 准备发票明细项\r\n      this.invoiceItems = debitNote.rsChargeList ? debitNote.rsChargeList.map(charge => {\r\n        return {\r\n          billNo: debitNote.billNo || \"\",\r\n          rctNo: \"\",  // 这里可能需要从父组件获取\r\n          serviceType: charge.chargeName || \"\",\r\n          chargeName: charge.chargeName || \"\",\r\n          remark: charge.remark || \"\",\r\n          paymentFlag: debitNote.isRecievingOrPaying == 0 ? \"收\" : \"付\",\r\n          quoteCurrency: charge.currencyCode || \"\",\r\n          unitPrice: charge.unitPrice || 0,\r\n          quantity: charge.quantity || 0,\r\n          unit: charge.unit || \"\",\r\n          settlementRate: charge.exchangeRate || 1,\r\n          settlementCurrency: debitNote.dnCurrencyCode || \"\",\r\n          taxRate: charge.taxRate || \"\",\r\n          taxIncludedTotal: charge.subtotal || 0,\r\n          invoiceItemName: charge.chargeName || \"\",\r\n          taxCode: \"\"\r\n        }\r\n      }) : []\r\n    },\r\n\r\n    // 准备发票对话框数据（使用现有发票信息）\r\n    prepareInvoiceDataWithExisting(debitNote, existingInvoice) {\r\n      // 使用现有发票信息设置表单数据\r\n      this.invoiceForm = {\r\n        // 基本发票信息\r\n        invoiceId: existingInvoice.invoiceId || debitNote.invoiceId || null,\r\n        invoiceCodeNo: existingInvoice.invoiceCodeNo || debitNote.invoiceCodeNo || \"\",\r\n        saleBuy: existingInvoice.saleBuy || (debitNote.isRecievingOrPaying == 0 ? \"sale\" : \"buy\"),\r\n        taxClass: existingInvoice.taxClass || \"\",\r\n        invoiceType: existingInvoice.invoiceType || \"增值税发票\",\r\n        mergeInvoice: existingInvoice.mergeInvoice || debitNote.mergeInvoice || \"0\",\r\n        invoiceOfficalNo: existingInvoice.invoiceOfficalNo || debitNote.invoiceOfficalNo || \"\",\r\n\r\n        // 公司和账户信息\r\n        invoiceBelongsTo: existingInvoice.invoiceBelongsTo || debitNote.companyBelongsTo || \"\",\r\n        richBankCode: existingInvoice.richBankCode || debitNote.bankAccountCode || \"\",\r\n        cooperatorId: existingInvoice.cooperatorId || debitNote.clearingCompanyId || \"\",\r\n        cooperatorBankCode: existingInvoice.cooperatorBankCode || debitNote.clearingCompanyBankAccount || \"\",\r\n        richCompanyTitle: existingInvoice.richCompanyTitle || debitNote.bankAccountName || \"\",\r\n        cooperatorCompanyTitle: existingInvoice.cooperatorCompanyTitle || debitNote.clearingCompanyName || \"\",\r\n\r\n        // 项目和订单信息\r\n        officalChargeNameSummary: existingInvoice.officalChargeNameSummary || \"\",\r\n        relatedOrderNo: existingInvoice.relatedOrderNo || \"\",\r\n\r\n        // 税号和银行信息\r\n        richVatSerialNo: existingInvoice.richVatSerialNo || \"\",\r\n        cooperatorVatSerialNo: existingInvoice.cooperatorVatSerialNo || \"\",\r\n        richBankFullname: existingInvoice.richBankFullname || \"\",\r\n        cooperatorBankFullname: existingInvoice.cooperatorBankFullname || \"\",\r\n        richBankAccount: existingInvoice.richBankAccount || \"\",\r\n        cooperatorBankAccount: existingInvoice.cooperatorBankAccount || debitNote.clearingCompanyBankAccount || \"\",\r\n\r\n        // 备注和日期信息\r\n        invoiceRemark: existingInvoice.invoiceRemark || \"\",\r\n        expectedPayDate: existingInvoice.expectedPayDate || debitNote.expectedPaymentDate || \"\",\r\n        approvedPayDate: existingInvoice.approvedPayDate || \"\",\r\n        actualPayDate: existingInvoice.actualPayDate || debitNote.actualPaymentDate || \"\",\r\n\r\n        // 发票金额信息\r\n        invoiceExchangeRate: existingInvoice.invoiceExchangeRate || \"1\",\r\n        invoiceCurrencyCode: existingInvoice.invoiceCurrencyCode || debitNote.dnCurrencyCode || \"RMB\",\r\n        invoiceNetAmount: existingInvoice.invoiceNetAmount || (debitNote.isReceivable ? debitNote.billReceivable : debitNote.billPayable),\r\n        invoiceStatus: existingInvoice.invoiceStatus || (debitNote.invoiceStatus === \"issued\" ? \"1\" : \"0\"),\r\n        belongsToMonth: existingInvoice.belongsToMonth || this.formatCurrentMonth(),\r\n\r\n        // RCT关联信息\r\n        rctId: existingInvoice.rctId || this.rctId || null\r\n      }\r\n\r\n      // 使用现有发票明细项，如果没有则使用debitNote的费用明细\r\n      if (existingInvoice.invoiceItems && existingInvoice.invoiceItems.length > 0) {\r\n        this.invoiceItems = existingInvoice.invoiceItems\r\n      } else {\r\n        // 准备发票明细项\r\n        this.invoiceItems = debitNote.rsChargeList ? debitNote.rsChargeList.map(charge => {\r\n          return {\r\n            billNo: debitNote.billNo || \"\",\r\n            rctNo: \"\",  // 这里可能需要从父组件获取\r\n            serviceType: charge.chargeName || \"\",\r\n            chargeName: charge.chargeName || \"\",\r\n            remark: charge.remark || \"\",\r\n            paymentFlag: debitNote.isRecievingOrPaying == 0 ? \"收\" : \"付\",\r\n            quoteCurrency: charge.currencyCode || \"\",\r\n            unitPrice: charge.unitPrice || 0,\r\n            quantity: charge.quantity || 0,\r\n            unit: charge.unit || \"\",\r\n            settlementRate: charge.exchangeRate || 1,\r\n            settlementCurrency: debitNote.dnCurrencyCode || \"\",\r\n            taxRate: charge.taxRate || \"\",\r\n            taxIncludedTotal: charge.subtotal || 0,\r\n            invoiceItemName: charge.chargeName || \"\",\r\n            taxCode: \"\"\r\n          }\r\n        }) : []\r\n      }\r\n    },\r\n\r\n    // 格式化当前月份为 yyyyMM 格式（如 202503）\r\n    formatCurrentMonth() {\r\n      const date = new Date()\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, \"0\")\r\n      return `${year}${month}`\r\n    },\r\n\r\n    // 处理发票对话框提交\r\n    async handleInvoiceSubmit(formData) {\r\n      try {\r\n        let response\r\n\r\n        // 根据是否有invoiceId决定是新增还是修改\r\n        if (formData.invoiceId) {\r\n          // 修改发票\r\n          response = await updateVatinvoice(formData)\r\n        } else {\r\n          // 新增发票\r\n          response = await addVatinvoice(formData)\r\n        }\r\n\r\n        if (response.code === 200) {\r\n          // 更新发票状态\r\n          if (this.currentDebitNote) {\r\n            this.currentDebitNote.invoiceStatus = formData.invoiceStatus === \"1\" ? \"issued\" : \"unissued\"\r\n\r\n            // 将发票ID写入到debitNote中\r\n            let invoiceId = null\r\n            if (response.data && response.data.invoiceId) {\r\n              invoiceId = response.data.invoiceId\r\n              this.currentDebitNote.invoiceId = invoiceId\r\n            } else if (formData.invoiceId) {\r\n              invoiceId = formData.invoiceId\r\n              this.currentDebitNote.invoiceId = invoiceId\r\n            }\r\n\r\n            // 更新发票相关字段\r\n            this.currentDebitNote.invoiceCodeNo = formData.invoiceCodeNo || \"\"\r\n            this.currentDebitNote.invoiceOfficalNo = formData.invoiceOfficalNo || \"\"\r\n            this.currentDebitNote.invoiceType = formData.invoiceType || \"\"\r\n            this.currentDebitNote.mergeInvoice = formData.mergeInvoice || \"0\"\r\n            // 更新分账单的发票状态为已申请\r\n            this.currentDebitNote.invoiceStatus = \"applied\"\r\n\r\n            // 调用接口更新debitNote中的invoiceId\r\n            if (invoiceId && this.currentDebitNote.debitNoteId) {\r\n              try {\r\n                const updateData = {\r\n                  debitNoteId: this.currentDebitNote.debitNoteId,\r\n                  invoiceId: invoiceId,\r\n                  invoiceCodeNo: formData.invoiceCodeNo || \"\",\r\n                  invoiceOfficalNo: formData.invoiceOfficalNo || \"\",\r\n                  invoiceType: formData.invoiceType || \"\",\r\n                  mergeInvoice: formData.mergeInvoice || \"0\",\r\n                  invoiceStatus: \"applied\"\r\n                }\r\n\r\n                // 更新分账单\r\n                const updateResponse = await updateDebitNote(updateData)\r\n                if (updateResponse.code === 200) {\r\n                  this.$message.success(\"发票信息已更新到分账单\")\r\n                } else {\r\n                  console.warn(\"更新分账单发票信息失败:\", updateResponse.msg)\r\n                }\r\n              } catch (updateError) {\r\n                console.error(\"更新分账单发票信息失败:\", updateError)\r\n              }\r\n            }\r\n\r\n            // 通知父组件数据变化\r\n            this.$emit(\"return\", this.debitNoteList)\r\n          }\r\n\r\n          this.$message.success(\"发票保存成功\")\r\n        } else {\r\n          this.$message.error(response.msg || \"发票保存失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"发票保存失败:\", error)\r\n        this.$message.error(\"发票保存失败，请重试\")\r\n      }\r\n\r\n      this.invoiceDialogVisible = false\r\n    },\r\n\r\n    // 处理发票对话框取消\r\n    handleInvoiceCancel() {\r\n      this.invoiceDialogVisible = false\r\n    },\r\n    applyUnlock(row) {\r\n      // 查看发票状态,已开票的不能申请解锁\r\n      if (row.invoiceStatus === \"issued\") {\r\n        this.$message.error(\"已开票的分账单不能申请解锁\")\r\n        return\r\n      }\r\n\r\n      this.$emit(\"applyUnlock\", row)\r\n    },\r\n    setComplete(row) {\r\n      try {\r\n        this.$confirm(\"确定要将该分账单设置为已确认状态吗？此操作将禁用整条数据及其费用明细。\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          // 修改状态为已确认\r\n          row.billStatus = \"confirmed\"\r\n\r\n          // 设置所有费用明细的isAccountConfirmed为'1'，使其被禁用\r\n          if (row.rsChargeList && row.rsChargeList.length > 0) {\r\n            row.rsChargeList.forEach(charge => {\r\n              charge.isAccountConfirmed = \"1\"\r\n            })\r\n          }\r\n\r\n          // 通知父组件状态变更\r\n          this.$emit(\"setComplete\", row)\r\n\r\n          // 提示用户\r\n          this.$message({\r\n            type: \"success\",\r\n            message: \"分账单已设置为已确认状态\"\r\n          })\r\n        }).catch(() => {\r\n          // 用户取消操作\r\n        })\r\n      } catch (error) {\r\n        console.error(\"设置分账单状态失败:\", error)\r\n        this.$message.error(\"设置分账单状态失败\")\r\n      }\r\n    },\r\n    changeCurrency(row, currency) {\r\n      row.dnCurrencyCode = currency\r\n    },\r\n    selectBankAccount(row, bankAccount) {\r\n      row.bankAccountCode = bankAccount.bankAccountCode\r\n      row.bankAccountName = bankAccount.bankAccountName\r\n    },\r\n    handleSelectCompany(row, company) {\r\n      row.clearingCompanyId = company.companyId\r\n      row.clearingCompanyName = company.companyShortName\r\n    },\r\n    addDebitNote() {\r\n      this.$emit(\"addDebitNote\")\r\n    },\r\n    currency,\r\n\r\n    // 展开/收起行\r\n    handleExpandChange(row, expandedRows) {\r\n      this.expandedRows = expandedRows\r\n    },\r\n\r\n    // 创建分账单\r\n    async createDebitNote(row) {\r\n      try {\r\n\r\n      } catch (error) {\r\n        console.error(\"创建分账单失败:\", error)\r\n        this.$message.error(\"创建分账单失败\")\r\n      }\r\n    },\r\n    // 删除分账单\r\n    async deleteDebitNote(row) {\r\n      try {\r\n        await this.$confirm(\"确定要删除该分账单吗？\", \"提示\", {\r\n          type: \"warning\"\r\n        })\r\n        this.$emit(\"deleteItem\", row)\r\n      } catch (error) {\r\n        if (error !== \"cancel\") {\r\n          console.error(\"删除分账单失败:\", error)\r\n          this.$message.error(\"删除分账单失败\")\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理费用数据变化\r\n    handleChargeDataChange(row, chargeData) {\r\n      let billReceivable = 0\r\n      let billPayable = 0\r\n\r\n      // 统计chargeData的费用\r\n      if (this.isReceivable) {\r\n        // 应收\r\n        chargeData.forEach(item => {\r\n          // 使用currency.js计算\r\n          billReceivable = currency(billReceivable).add(item.subtotal).toString()\r\n        })\r\n      } else {\r\n        // 应付\r\n        chargeData.forEach(item => {\r\n          billPayable = currency(billPayable).add(item.subtotal).toString()\r\n        })\r\n      }\r\n      row.billReceivable = billReceivable\r\n      row.billPayable = billPayable\r\n\r\n      row.rsChargeList = chargeData\r\n      // 通知父组件数据变化\r\n      this.$emit(\"return\", this.debitNoteList)\r\n    },\r\n\r\n    // 处理费用选择\r\n    handleChargeSelection(row, selectedCharges) {\r\n      const index = this.localDebitNoteList.findIndex(item => item === row)\r\n      if (index !== -1) {\r\n        this.localDebitNoteList[index].selectedCharges = selectedCharges\r\n        // 通知父组件数据变化\r\n        this.$emit(\"update:debitNoteList\", this.localDebitNoteList)\r\n      }\r\n    },\r\n\r\n    // 复制费用\r\n    handleCopyFreight(charge) {\r\n      this.$emit(\"copyFreight\", charge)\r\n    },\r\n\r\n    // 删除费用项\r\n    handleDeleteItem(charge) {\r\n      this.$emit(\"deleteItem\", charge)\r\n    },\r\n\r\n    // 删除所有费用\r\n    handleDeleteAll() {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n\r\n    // 获取账单状态类型\r\n    getBillStatusType(status) {\r\n      const statusMap = {\r\n        \"draft\": \"info\",\r\n        \"confirmed\": \"success\",\r\n        \"closed\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取账单状态文本\r\n    getBillStatusText(status) {\r\n      const statusMap = {\r\n        \"draft\": \"草稿\",\r\n        \"confirmed\": \"已确认\",\r\n        \"closed\": \"已关闭\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    },\r\n\r\n    // 获取发票状态类型\r\n    getInvoiceStatusType(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"info\",\r\n        \"issued\": \"success\",\r\n        \"applied\": \"warning\",\r\n        \"canceled\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取发票状态文本\r\n    getInvoiceStatusText(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"未开票\",\r\n        \"issued\": \"已开票\",\r\n        \"applied\": \"已申请\",\r\n        \"canceled\": \"已作废\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    },\r\n\r\n    // 获取销账状态类型\r\n    getWriteoffStatusType(status) {\r\n      const statusMap = {\r\n        \"unwritten\": \"info\",\r\n        \"partial\": \"warning\",\r\n        \"written\": \"success\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取销账状态文本\r\n    getWriteoffStatusText(status) {\r\n      const statusMap = {\r\n        \"unwritten\": \"未销账\",\r\n        \"partial\": \"部分销账\",\r\n        \"written\": \"已销账\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.expand-content {\r\n  padding: 20px;\r\n  background-color: #f9f9f9;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  margin: 10px 0;\r\n}\r\n\r\n.charge-list-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n\r\n  span {\r\n    font-weight: bold;\r\n    font-size: 16px;\r\n    color: #303133;\r\n  }\r\n}\r\n\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n// 覆盖 Element UI 表格样式\r\n:deep(.el-table) {\r\n  .el-table__expanded-cell {\r\n    padding: 0;\r\n\r\n    .expand-content {\r\n      margin: 0;\r\n      border: none;\r\n      background-color: transparent;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}